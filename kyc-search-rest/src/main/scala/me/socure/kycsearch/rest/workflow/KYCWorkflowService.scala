package me.socure.kycsearch.rest.workflow

import com.google.i18n.phonenumbers.{NumberParseException, PhoneNumberUtil}
import com.google.inject.Injector
import me.socure.kycsearch.rest.utils.BestMatchEntityPresenterUtils.{getAssociatedAddressList, getAssociatedEmailList, getAssociatedPhoneNumberList}
import me.socure.kycsearch.model.weights.KycMatchWeightsGetter
import com.socure.domain.socure.scoring.RuleCodes
import com.socure.domain.socure.scoring.RuleCodes.EX_UNIQUE_ADDRESS_OR_DOB
import me.socure.common.data.core.provider._
import me.socure.common.json4s.serializer.TimestampJodaDateTimeSerializer
import me.socure.common.kyc.CommonUtil.{findDataSource, findVendor}
import me.socure.common.kyc.factory.QueryParserFactory._
import me.socure.common.kyc.model.PiiAttribute.{DO<PERSON><PERSON>, EmailMatch, FirstNameMatch, MobileNumberMatch, SSNMatch, SurNameMatch}
import me.socure.common.kyc.model.es.result.{Elastic4sResult, IdentityRecord, IdentityRecordRemoved, PIIField, PIIRowIDs, Records}
import me.socure.common.kyc.model.es.result.Elastic4sResultExtensions._
import me.socure.common.kyc.model.{AddressObject, AddressObjectOption, AnchorDeceasedRecord, BestMatchEntity, BestMatchEntityWithAlias, City, CleanedStreetAddress, CustomKYCPreferences, DOB, DataSources, EmailObject, FeatureFlags, Features, KYCSearchRequestResolved, KycEntityResponse, KycEntitySearchRequest, MatchMetadata, MergeEntityConfig, MobileNumber, NationalId, PhoneNumberObject, PrefillMatchedName, State, StreetAddress, Workflows, ZipCode}
import me.socure.common.kyc.source.SourceAttributionUtil.{getSourceAttributions, getVendors}
import me.socure.common.kyc.util.{CommonUtil, FutureUtils, SSNUtil}
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.sqs.fallback.producer.SqsWithFallbackProducer
import me.socure.common.transaction.id.TrxId
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.ecbsv.data.inhouse.common.InhouseDataModel
import me.socure.ecbsv.database.model.ecbsv_inhouse_data.EcbsvStoredIntelligenceDynamoModel
import me.socure.kyc.datasource.anchor.dao.AnchorDeceasedDao
import me.socure.kyc.datasource.attom.{AttomDataSearchService, AttomSearchAddressInput}
import me.socure.kyc.search.merge.entity.{MergedEntityUtil, NationalIdRefiner}
import me.socure.kyc.search.merge.entity.MergedEntityUtil.{EntityMergesCache, validateFrequencyBasedMergeLogic, validateFrequencyDistribution, validateFrequencyDistributionForAddress}
import me.socure.kyc.search.unique.id.{EntityIdGenerator, EntityUnificationDao}
import me.socure.kycsearch.matcher.DOBMatchers.matchBDFuzzy
import me.socure.kycsearch.matcher.NameMatchers.findClosestMatchedNames
import me.socure.kycsearch.matcher.NationalIDMacthers.{cleanNationalId, matchNationalId}
import me.socure.kycsearch.matcher._
import me.socure.kycsearch.model.BestKYCMatch
import me.socure.kycsearch.rest.error.ErrorResponses
import me.socure.kycsearch.rest.factory.ElasticSearchQueryFactory
import me.socure.kycsearch.rest.helper.AnchorLookupUtil
import me.socure.kycsearch.rest.helper.KYCPlusHelper.{PII_MASKED_VALUE, getUnsharedPIIMaskedBME, validateKYCPlusBMECriteria}
import me.socure.kycsearch.rest.helper.SQSMessagePushHelper.findVendorFromClusterID
import me.socure.kycsearch.rest.internal.entity.data.InternalEntityDataHandler
import me.socure.kycsearch.rest.model.{KYCWorkflowServiceConfig, UniqueEntityIdResult}
import me.socure.kycsearch.rest.socureid.SocureIDClient
import me.socure.kycsearch.rest.utils.{BestMatchEntityPresenterUtils, DeceasedRecordService, EmailProcessingService}
import me.socure.kycsearch.rest.vendors.ecbsv.{EcbsvInHouseDataKycTransactionProcess, EcbsvInhouseHelper, KYCBestMatchWithWeights}
import me.socure.kycsearch.rulecodes.common.DOBResolverHelper.findClosestMatchedDOB
import me.socure.kycsearch.rulecodes.common.NationalIDResolverHelper.{getClosestMatchedSSN, getResolved9DigitSSN, hasMatch, isSSNIssuedStateNotInAssociatedAddresses}
import me.socure.kycsearch.rulecodes.common.{AddressResolverHelper, ComputedResultsDTO, DOBResolverHelper, GenericHelper}
import me.socure.kycsearch.rulecodes.entity.SigmaRuleCodeFactory
import me.socure.kycsearch.rulecodes.kyc.KYCRuleCodeFactory
import me.socure.kycsearch.rulecodes.rulecode.boolToDouble
import me.socure.model.ErrorResponse
import me.socure.transaction.resolved.entity.common.model.queue._
import me.socure.transaction.resolved.entity.common.model.unified.entity.{EntityRecord, EntitySearchResults}
import net.codingwell.scalaguice.InjectorExtensions._
import org.apache.commons.lang3.StringUtils
import org.apache.commons.validator.routines.EmailValidator
import org.joda.time.DateTime
import org.json4s.ext.EnumNameSerializer
import org.json4s.native.Serialization
import org.json4s.{DefaultFormats, Formats}
import org.opensearch.index.query.QueryBuilders
import org.opensearch.search.builder.SearchSourceBuilder
import org.slf4j.{Logger, LoggerFactory}

import java.io.ByteArrayOutputStream
import java.util.Base64
import java.util.concurrent.{TimeUnit, TimeoutException}
import java.util.zip.GZIPOutputStream
import javax.annotation.Nullable
import javax.inject.{Inject, Named}
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.{Duration, DurationInt}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.control.{Breaks, NonFatal}
import scala.util.{Failure, Success, Try}

class KYCWorkflowService @Inject()(@Named("equifaxQueryService") elasticQueryService: QueryService,
                                   @Named("enformionQueryService") enfElasticQueryService: QueryService,
                                   mockedQueryService: MockedQueryService,
                                   @Named("docVQueryService") docVElasticQueryService: Option[ElasticQueryService],
                                   @Named("docVQueryTimeOut") docVQueryTimeOut: Int,
                                   @Named("boldQueryService") boldElasticQueryService: Option[QueryService],
                                   @Named("obitScrapperElasticQueryService") obitScrapperElasticQueryService: Option[QueryService],
                                   resultMatcher: ResultMatcher,
                                   @Nullable @Named("UnifiedEntitySQSClient") unifiedEntitySQSClient: SqsWithFallbackProducer,
                                   @Named("esResultPush") esResultPush: Boolean,
                                   @Named("unifiedEntityQueryService") unifiedEntityQueryService: ElasticQueryService,
                                   @Named("enableUnifiedEntitySource") enableUnifiedEntitySource: Boolean = false,
                                   @Named("anchorEnabled") anchorEnabled: Boolean = false,
                                   @Nullable anchorLookupDao: AnchorDeceasedDao,
                                   @Named("ecbsvInhouseEnabled") ecbsvInhouseEnabled: Boolean = false,
                                   injector: Injector,
                                   @Nullable entityUnificationDao: EntityUnificationDao,
                                   sqsClient: SqsWithFallbackProducer,
                                   @Named("indexName") equifaxIndex: String,
                                   @Named("enfIndexName") enformionIndex: String,
                                   @Nullable entityIdGenerator: EntityIdGenerator,
                                   kycMatchWeightsGetter: KycMatchWeightsGetter,
                                   @Named("uniqueIdSQSPushEnabled") uniqueIdSQSPushEnabled: Boolean = true,
                                   internalEntityDataHandler: InternalEntityDataHandler,
                                   deceasedRecordService: DeceasedRecordService,
                                   kycWorkflowServiceConfig: KYCWorkflowServiceConfig,
                                   addressAnalyzer: AddressAnalyzer,
                                   emailProcessingService: EmailProcessingService,
                                   attomDataSearchService: AttomDataSearchService,
                                   @Nullable dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate,
                                   nationalIdRefiner: NationalIdRefiner
                                  )(implicit ec: ExecutionContext) {


  private val trxLogger = TransactionAwareLoggerFactory.getLogger(getClass)

  private val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[KYCWorkflowService])

  private val defaultMatchWeights = kycMatchWeightsGetter.get("default-match-weights")
  private val syntheticBmePriority: Array[DataSources.Value] = Array(DataSources.Equifax, DataSources.LocateSmarter, DataSources.Enformion) // Don't change order of element in an array
  private val dataSourcesToSkipForBaseEntity = Set(DataSources.Bold, DataSources.ASL, DataSources.EDV, DataSources.Attom)


  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.elasticMetricPrefix.toString)
  val serviceName: String = "kyc"
  private implicit val formats: Formats = DefaultFormats ++ List(new EnumNameSerializer(MessageType), TimestampJodaDateTimeSerializer())
  private val uniqueEntityIdHandler: UniqueEntityIdHandler = injector.instance[UniqueEntityIdHandler]
  class CamelCaseString(val s: String) {
    def toCamelCase(): String = {
      var c: Char = ' '
      s.map(chr => {
        val r = if (c == ' ') chr.toUpper else chr
        c = chr
        r
      })
    }
  }
  private implicit def camelCaseStringImplicit(s:String) = new CamelCaseString(s)

  private def getObitMatch(bestOfThreeSourcesKYC: Option[BestKYCMatch], scoredObitMatchesEntity: Array[BestKYCMatch],  hitMap: Map[DataSources.DataSource, Array[BestKYCMatch]], source: String = "kyc") = {
    if(bestOfThreeSourcesKYC.isDefined && !scoredObitMatchesEntity.isEmpty && hasObitMatch(bestOfThreeSourcesKYC.get, scoredObitMatchesEntity.head, hitMap)) {
      val deceasedDate = getDeceasedDate(scoredObitMatchesEntity.head)
      deceasedDate
    }
    else {
      None
    }
  }

  def hasObitMatch(bestMatchEntity: BestKYCMatch, obitRecord: BestKYCMatch,  hitsMap: Map[DataSources.DataSource, Array[BestKYCMatch]]): Boolean = {
    def getScore(aliases: Array[String], input: Array[String], scoreIfTrue: Double): Double = if (aliases.exists(alias => input.exists(removeNonAlphanumeric(_).equalsIgnoreCase(removeNonAlphanumeric(alias))))) scoreIfTrue else 0.0
    def getScoreDOB(aliases: Array[String], input: Array[String], scoreIfTrue: Double): Double = if (aliases.exists(alias => input.exists(inputDob => matchBDFuzzy(DOB(alias), DOB(inputDob))))) scoreIfTrue else 0.0
    def removeNonAlphanumeric(str: String): String = {
      str.replaceAll("[^a-zA-Z0-9]", "")
    }
    def getAddressScore(bmeRecord: BestKYCMatch, obitRecord: BestKYCMatch, scoreIfTrue: Double) : Double = {
      val streetAddressMatch = if(bmeRecord.cluster.streetAddress.exists(alias => obitRecord.cluster.streetAddress.exists(_.equalsIgnoreCase(alias))))  defaultMatchWeights.streetAddressWeight else 0.0
      val cityMatch = if(bmeRecord.cluster.city.exists(alias => obitRecord.cluster.city.exists(_.equalsIgnoreCase(alias)))) defaultMatchWeights.cityWeight else 0.0
      val stateMatch = if(bmeRecord.cluster.state.exists(alias => obitRecord.cluster.state.exists(_.equalsIgnoreCase(alias)))) defaultMatchWeights.stateWeight else 0.0
      val zipMatch = if(bmeRecord.cluster.zipCode.exists(alias => obitRecord.cluster.zipCode.exists(_.equalsIgnoreCase(alias)))) defaultMatchWeights.zipWeight else 0.0
      if((streetAddressMatch + cityMatch + stateMatch + zipMatch) >= 4.0) scoreIfTrue else 0.0
    }

    var score = {
        getScore(bestMatchEntity.cluster.firstName, obitRecord.cluster.firstName, 0.5) +
        getScore(bestMatchEntity.cluster.surName, obitRecord.cluster.surName, 0.5) +
        getScoreDOB(bestMatchEntity.cluster.dob, obitRecord.cluster.dob, 1.0) +
          getAddressScore(bestMatchEntity,obitRecord, 1.0)
    }
    if(score <= 2.5 ) {
      val freqValue =  if(validateFrequencyBasedMergeLogic(bestMatchEntity, obitRecord, hitsMap, EntityMergesCache.empty)){
        1.0
      }
      else {
        0.0
      }
      score = score + freqValue
    }
    score >= 2.5
  }

  /**
   *  - querying
   *     - if both kyc & entity requested
   *       - if both have same DOB preferences, then reuse result
   *       - else make another call with 2nd preferences
   *     - else if only kyc requested, then make only kyc based req
   *     - else if only entity requested, then make only entity based req
   *   - result matcher
   *     - if both kyc & entity requested
   *       - if both have same preferences, then reuse result
   *       - else recalculate results with different preferences
   *     - else if only kyc requested, then calculate only kyc matches
   *     - else if only entity requested, then calculate only entity matches
   */
  def process(searchRequest: KYCSearchRequestResolved, enformionMergeEnabled: Boolean, isHealthCheck: Boolean = false): Future[Either[ErrorResponse, KycEntityResponse]] = {
    val resolvedReq = searchRequest.resolvedReq
    val originalReq = searchRequest.originalReq
    val mergeEntityConfig = originalReq.mergeEntityConfig
    val is4DigitSsn = searchRequest.processingMetadata.is4DigitSsn
    val featureFlags = searchRequest.featureFlags

    var socureIDClient: Option[SocureIDClient] = None

    if (resolvedReq.socureIdEnabled) {
      socureIDClient = Some(injector.instance[SocureIDClient])
    }


    val ecbsvInHouseDataKycTransactionProcess: EcbsvInHouseDataKycTransactionProcess = if (ecbsvInhouseEnabled && socureIDClient.isEmpty) {
      implicit val ecbsvInhouseHelper = injector.instance[EcbsvInhouseHelper]
      new EcbsvInHouseDataKycTransactionProcess(resolvedReq.nationalId.map(_.value))
    } else {
      class DummyEcbsvInhouseHelper extends EcbsvInhouseHelper(null, null, null, null) {
        override def getDecryptedInHouseData(ssn: String): Future[List[(EcbsvStoredIntelligenceDynamoModel, InhouseDataModel)]] = {
          Future.successful(List.empty)
        }

        override def getBestEcbsvMatch(requestRaw: KYCSearchRequestResolved, records: Elastic4sResult): Array[KYCBestMatchWithWeights] = {
          Array.empty
        }
      }
      implicit val ecbsvInhouseHelper: EcbsvInhouseHelper = new DummyEcbsvInhouseHelper
      new EcbsvInHouseDataKycTransactionProcess(None)
    }

    val mockDataId = searchRequest.resolvedReq.mockDataId.getOrElse(0.toLong)
    val identityEsRequestKyc = ElasticSearchQueryFactory.generateIdentityQuery(resolvedReq, entity = false, Some(kycWorkflowServiceConfig))
    val deceasedIdentityEsRequestKyc = ElasticSearchQueryFactory.generateDeceasedQuery(resolvedReq, entity = false)
    val nationalIdRequestOpt = ElasticSearchQueryFactory.generateNationalIdQuery(resolvedReq)
    val isDeceasedCheckRequested: Boolean = resolvedReq.modulesEnabled.map(_.toLowerCase).contains("deceasedcheck")

    val kycRequested = resolvedReq.workflows.contains(Workflows.Kyc)
    val entityRequested = resolvedReq.workflows.contains(Workflows.Entity)

    val isDecisionEnabled = resolvedReq.modulesEnabled.contains("ModuleDecisioning")
    val isKycPlusEnabled: Boolean = resolvedReq.customPreferencesKyc.isKycPlusEnabled match {
      case Some(isKycPlusProvisioned) =>
        isKycPlusProvisioned && resolvedReq.modulesEnabled.map(_.toLowerCase).contains("kycplus")
      case _ =>
        false
    }
    val isSinglePiiRequest = resolvedReq.isSinglePiiRequest

    val (eqxQueryService, enfQueryService, docVQueryService: Option[QueryService], boldQueryService : Option[QueryService]) =
      if (mockDataId != 0)
        (mockedQueryService, mockedQueryService, Some(mockedQueryService), Some(mockedQueryService))
      else
        (elasticQueryService, enfElasticQueryService, docVElasticQueryService, boldElasticQueryService)

    val dataSourcesToQueryServiceMap: Map[DataSources.DataSource, QueryService] = Map(
      DataSources.Equifax -> eqxQueryService,
      DataSources.Enformion -> enfQueryService,
      DataSources.LocateSmarter -> enfQueryService
    ) ++ Seq(
      docVQueryService.map(qs => DataSources.DocV -> qs),
      boldQueryService.map(qs => DataSources.Bold -> qs),
      boldQueryService.map(qs => DataSources.ASL -> qs), // Bold, ASL, EDV data are stored in same ES index and dynamo table
      boldQueryService.map(qs => DataSources.EDV -> qs)
    ).flatten.toMap

    implicit val trxId = resolvedReq.transactionId match {
      case Some(transactionId) => TrxId(transactionId)
      case None => TrxId("Unknown")
    }

    val identitySearchResponseFuture: Future[Elastic4sResult] = if (kycRequested) {
      if (socureIDClient.isEmpty) {
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          queryType = Some("kyc"),
          vendorName = Some("equifax"),
          tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = eqxQueryService.execute(identityEsRequestKyc, mockDataId, "EQX", "IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )().flatMap(_.withCleanedDataAsync(resolvedReq))

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("Unknown failure", e)
            metrics.increment("identity.search.exception", "class:" + e.getClass.getSimpleName)
            Future.failed(e)
        }
      } else {
        socureIDClient.get.getSocureIdIdentityResults(resolvedReq)
      }
    }
    else Future.successful(Elastic4sResult.empty)


    val enformionIdentitySearchResponseFuture: Future[Elastic4sResult] = if (enformionMergeEnabled && kycRequested && socureIDClient.isEmpty) {
      val tags = MetricTags(
        serviceName = Some(serviceName),
        isInternal = Some(false),
        mSource = Some("client"),
        queryType = Some("kyc"),
        vendorName = Some("enformion"),
        tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
      )
      val execRequest = enfQueryService.execute(identityEsRequestKyc, mockDataId, "ENF", "IDENTITY_REQUEST")
        .withMetricTagsV2(
          metrics = prefixedMetrics,
          baseTags = tags
        )().flatMap(_.withCleanedDataAsync(resolvedReq))

      execRequest.recoverWith {
        case e: Throwable =>
          logger.error("Unknown failure", e)
          metrics.increment("identity.search.exception.enformion", "class:" + e.getClass.getSimpleName)
          Future.failed(e)
      }
    }
    else Future.successful(Elastic4sResult.empty)

    val docVIdentitySearchResponseFuture: Future[Elastic4sResult] = if (kycRequested && docVQueryService.isDefined && socureIDClient.isEmpty) {
      val tags = MetricTags(
        serviceName = Some(serviceName),
        isInternal = Some(false),
        mSource = Some("client"),
        queryType = Some("kyc"),
        vendorName = Some("docv"),
        tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
      )
      val execRequest = docVQueryService.get.execute(identityEsRequestKyc, mockDataId, "DOCV", "IDENTITY_REQUEST")
        .withMetricTagsV2(
          metrics = prefixedMetrics,
          baseTags = tags
        )()

      execRequest.recoverWith {
        case e: Throwable =>
          logger.error("Unknown failure", e)
          metrics.increment("identity.search.exception.docv", "class:" + e.getClass.getSimpleName)
          Future.failed(e)
      }
    }
    else Future.successful(Elastic4sResult(Array.empty))

    val boldIdentitySearchResponseFuture: Future[Elastic4sResult] = if (kycRequested && boldQueryService.isDefined && socureIDClient.isEmpty) {
      val tags = MetricTags(
        serviceName = Some(serviceName),
        isInternal = Some(false),
        mSource = Some("client"),
        queryType = Some("kyc"),
        vendorName = Some("bold"),
        tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
      )
      val execRequest = boldQueryService.get.execute(identityEsRequestKyc, mockDataId, "BLD", "IDENTITY_REQUEST")
        .withMetricTagsV2(
          metrics = prefixedMetrics,
          baseTags = tags
        )()

      execRequest.recoverWith {
        case e: Throwable =>
          logger.error("identity.search.exception.bold class:" + e.getClass.getSimpleName, e)
          Future.failed(e)
      }
    }
    else Future.successful(Elastic4sResult(Array.empty))

    val deceasedFutures:List[Future[Elastic4sResult]] = deceasedRecordService.getElasticSearchFuture(socureIDClient, kycRequested, mockDataId, resolvedReq, entityRequested)

    val bestMatchedInternalEntityFuture = if (!isHealthCheck && !resolvedReq.isSinglePiiRequest) {
      internalEntityDataHandler.fetchBestMatchedInternalEntity(searchRequest)
        .map {
          case Right(res) => (res, false)
          case Left(_: TimeoutException) => (None, true)
          case Left(_) => (None, false)
        }
    } else Future.successful((None, false))

    val unifiedIdentitySearchResponseFuture: Future[Elastic4sResult] = if (kycRequested && enableUnifiedEntitySource && socureIDClient.isEmpty) {
      val tags = MetricTags(
        serviceName = Some(serviceName),
        isInternal = Some(false),
        mSource = Some("client"),
        queryType = Some("kyc"),
        vendorName = Some("unified_entity"),
        tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
      )
      val execRequest = unifiedEntityQueryService.execute(identityEsRequestKyc, mockDataId, "UNI", "IDENTITY_REQUEST")
        .withMetricTagsV2(
          metrics = prefixedMetrics,
          baseTags = tags
        )()

      execRequest.recoverWith {
        case e: Throwable =>
          logger.error("Unknown failure", e)
          metrics.increment("identity.search.exception.unified_entity", "class:" + e.getClass.getSimpleName)
          Future.failed(e)
      }
    }
    else Future.successful(Elastic4sResult.empty)

    //as of now only DOB preferences modify the query, so we need to check and if it differs send another request to ES
    val identitySearchResponseEntityFuture: Future[Elastic4sResult] = if (entityRequested) {
      if (socureIDClient.isEmpty) {
        if (resolvedReq.sameDobPreferences() && kycRequested) {
          identitySearchResponseFuture
        } else {
          val identityEsRequestEntity = ElasticSearchQueryFactory.generateIdentityQuery(resolvedReq, entity = true, Some(kycWorkflowServiceConfig))
          val tags = MetricTags(
            serviceName = Some(serviceName),
            isInternal = Some(false),
            mSource = Some("client"),
            queryType = Some("entity"),
            vendorName = Some("equifax"),
            tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
          )
          val execRequest = eqxQueryService.execute(identityEsRequestEntity, mockDataId, "EQX", "IDENTITY_REQUEST")
            .withMetricTagsV2(
              metrics = prefixedMetrics,
              baseTags = tags
            )().flatMap(_.withCleanedDataAsync(resolvedReq))

          execRequest.recoverWith {
            case e: Throwable =>
              logger.error("Unknown failure", e)
              metrics.increment("identity.search.exception", "class:" + e.getClass.getSimpleName)
              Future.failed(e)
          }
        }
      } else if (kycRequested) {
        identitySearchResponseFuture
      } else {
        socureIDClient.get.getSocureIdIdentityResults(resolvedReq)
      }
    } else Future.successful(Elastic4sResult.empty)

    val enformionIdentitySearchResponseEntityFuture: Future[Elastic4sResult] = if (enformionMergeEnabled && entityRequested && socureIDClient.isEmpty) {
      if (resolvedReq.sameDobPreferences() && kycRequested) {
        identitySearchResponseFuture
      } else {
        val identityEsRequestEntity = ElasticSearchQueryFactory.generateIdentityQuery(resolvedReq, entity = true, Some(kycWorkflowServiceConfig))
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          queryType = Some("entity"),
          vendorName = Some("enformion"),
          tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = enfQueryService.execute(identityEsRequestEntity, mockDataId, "ENF", "IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )().flatMap(_.withCleanedDataAsync(resolvedReq))

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("Unknown failure", e)
            metrics.increment("identity.search.exception.enformion", "class:" + e.getClass.getSimpleName)
            Future.failed(e)
        }
      }
    } else Future.successful(Elastic4sResult.empty)

    val docVIdentitySearchResponseEntityFuture: Future[Elastic4sResult] = if (entityRequested && docVQueryService.isDefined && socureIDClient.isEmpty) {
      if (resolvedReq.sameDobPreferences() && kycRequested) {

        docVIdentitySearchResponseFuture
      }
      else {
        val identityEsRequestEntity = ElasticSearchQueryFactory.generateIdentityQuery(resolvedReq, entity = true, Some(kycWorkflowServiceConfig))
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          queryType = Some("entity"),
          vendorName = Some("docv"),
          tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = docVQueryService.get.execute(identityEsRequestEntity, mockDataId, "DOCV", "IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )()

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("identity.search.exception.docv class:" + e.getClass.getSimpleName, e)
            Future.failed(e)
        }
      }
    }
    else Future.successful(Elastic4sResult(Array.empty))

    val boldIdentitySearchResponseEntityFuture: Future[Elastic4sResult] = if (entityRequested && boldQueryService.isDefined && socureIDClient.isEmpty) {
      if (resolvedReq.sameDobPreferences() && kycRequested) {
        boldIdentitySearchResponseFuture
      }
      else {
        val identityEsRequestEntity = ElasticSearchQueryFactory.generateIdentityQuery(resolvedReq, entity = true, Some(kycWorkflowServiceConfig))
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          queryType = Some("entity"),
          vendorName = Some("bold"),
          tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = boldQueryService.get.execute(identityEsRequestEntity, mockDataId, "BLD", "IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )()

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("Unknown failure", e)
            metrics.increment("identity.search.exception.bold", "class:" + e.getClass.getSimpleName)
            Future.failed(e)
        }
      }
    }
    else Future.successful(Elastic4sResult(Array.empty))

    val unifiedIdentitySearchResponseEntityFuture: Future[Elastic4sResult] = if (entityRequested && enableUnifiedEntitySource && socureIDClient.isEmpty) {
      if (resolvedReq.sameDobPreferences() && kycRequested) {
        unifiedIdentitySearchResponseFuture
      } else {
        val identityEsRequestEntity = ElasticSearchQueryFactory.generateIdentityQuery(resolvedReq, entity = true, Some(kycWorkflowServiceConfig))
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          queryType = Some("entity"),
          vendorName = Some("unified_entity"),
          tags = Set("searchType:identity", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = unifiedEntityQueryService.execute(identityEsRequestEntity, mockDataId, "UNI", "IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )()

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("Unknown failure", e)
            metrics.increment("identity.search.exception.unified_entity", "class:" + e.getClass.getSimpleName)
            Future.failed(e)
        }
      }
    } else Future.successful(Elastic4sResult.empty)

    val nationalIdSearchResponseFuture = if (socureIDClient.isEmpty) {
      nationalIdRequestOpt.map(nationalIdRequest => {
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          queryType = Some("kyc"),
          tags = Set("searchType:ssn", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = eqxQueryService.execute(nationalIdRequest, mockDataId, "EQX", "NATIONAL_IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )().flatMap(_.withCleanedDataAsync(resolvedReq))

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("Unknown failure", e)
            metrics.increment("ssn.search.exception", "class:" + e.getClass.getSimpleName)
            Future.failed(e)
        }
      }).getOrElse(Future.successful(Elastic4sResult.empty))
    } else {
      socureIDClient.get.getSocureIdNationalIdResults(resolvedReq)
    }


    val enfNationalIdSearchResponseFuture = if (enformionMergeEnabled && socureIDClient.isEmpty) {
      nationalIdRequestOpt.map(nationalIdRequest => {
        val tags = MetricTags(
          serviceName = Some(serviceName),
          isInternal = Some(false),
          mSource = Some("client"),
          vendorName = Some("enformion"),
          queryType = Some("kyc"),
          tags = Set("searchType:ssn", "kycRequested-exval:" + kycRequested, "entityRequested-exxvl:" + entityRequested)
        )
        val execRequest = enfQueryService.execute(nationalIdRequest, mockDataId, "ENF", "NATIONAL_IDENTITY_REQUEST")
          .withMetricTagsV2(
            metrics = prefixedMetrics,
            baseTags = tags
          )().flatMap(_.withCleanedDataAsync(resolvedReq))

        execRequest.recoverWith {
          case e: Throwable =>
            logger.error("Unknown failure", e)
            metrics.increment("ssn.search.exception", "class:" + e.getClass.getSimpleName)
            Future.failed(e)
        }
      }).getOrElse(Future.successful(Elastic4sResult.empty))
    } else Future.successful(Elastic4sResult.empty)

    def futureDelayed(qType: String) = {
      FutureUtils.delay(docVQueryTimeOut.milliseconds) {
        (s"Time limit exceeded for DocV ES Search with QType: $qType")
      }
    }

    val docVIdentitySearchResponseFutureOrDelayed: Future[Elastic4sResult] =
      Future.firstCompletedOf(Seq(docVIdentitySearchResponseFuture.map(Right(_)), futureDelayed("kyc").map(Left(_))))
            .map {
              case Left(resultStr) =>
                logger.error(s"$resultStr")
                metrics.increment("docv.query.timed.out", "qType:kyc")
                Elastic4sResult(Array.empty)
              case Right(result) => result
            }

    val docVIdentitySearchResponseEntityFutureOrDelayed: Future[Elastic4sResult] =
      Future.firstCompletedOf(Seq(docVIdentitySearchResponseEntityFuture.map(Right(_)), futureDelayed("entity").map(Left(_))))
            .map {
              case Left(resultStr) =>
                logger.error(s"$resultStr")
                metrics.increment("docv.query.timed.out", "qType:entity")
                Elastic4sResult(Array.empty)
              case Right(result) => result
            }

    val attomResultFuture: Future[Elastic4sResult] = attomDataSearchService.searchAddress(
      AttomSearchAddressInput(
        street = resolvedReq.streetAddress.map(_.value),
        city = resolvedReq.city.map(_.value),
        state = resolvedReq.state.map(_.value),
        zipCode = resolvedReq.zipCode.map(_.value)
      )
    )

    val kycResponse = for {
      identityResultKyc <- identitySearchResponseFuture
      enformionIdentityResultKycInitial <- enformionIdentitySearchResponseFuture
      docVIdentityResult <- docVIdentitySearchResponseFutureOrDelayed
      boldIdentityResult <- boldIdentitySearchResponseFuture
      unifiedIdentityResultKyc <- unifiedIdentitySearchResponseFuture

      identityResultEntity <- identitySearchResponseEntityFuture
      enformionIdentityResultEntityInitial <- enformionIdentitySearchResponseEntityFuture
      docVIdentityResultEntity <- docVIdentitySearchResponseEntityFutureOrDelayed
      boldIdentityResultEntity <- boldIdentitySearchResponseEntityFuture
      unifiedIdentityResultEntity <- unifiedIdentitySearchResponseEntityFuture

      nationalIdResult <- nationalIdSearchResponseFuture
      enformionNationalIdResult <- enfNationalIdSearchResponseFuture
      (bestMatchedInternalEntity, isTimedOut) <- bestMatchedInternalEntityFuture
      attomResult <- attomResultFuture
    } yield {

      //Appending DocV, Bold results to Enformion results. So Enformion results will now have data from ENF + LS + DOCV + BOLD + ASL + EDV + ATTOM
      val enformionIdentityResultKyc: Elastic4sResult = enformionIdentityResultKycInitial.appended(docVIdentityResult).appended(boldIdentityResult).appended(attomResult)
      val enformionIdentityResultEntity: Elastic4sResult = enformionIdentityResultEntityInitial.appended(docVIdentityResultEntity).appended(boldIdentityResultEntity).appended(attomResult)

      val rootNonPartnerAccountId = resolvedReq.rootNonPartnerAccountId
      if (rootNonPartnerAccountId.isEmpty) {
        trxLogger.debug("RootNonPartnerAccountId is empty")
      }
      val accountId = resolvedReq.accountId.getOrElse(-1L)
      val submissionDate = resolvedReq.submissionDate.map(_.value)

      /** Computing scored matches */
      val scoredMatchesKyc = {
        if (kycRequested) metrics.time("resultmatcher", "q_type:kyc")(resultMatcher.calculateMatches(searchRequest, identityResultKyc, entity = false).right.get)
        else Array.empty[BestKYCMatch]
      }

      val enformionScoredMatchesKyc = if (enformionMergeEnabled) {
        if (kycRequested) metrics.time("resultmatcher", "q_type:kyc", "vendor:enformion")(resultMatcher.calculateMatches(searchRequest, enformionIdentityResultKyc, entity = false).right.get)
        else Array.empty[BestKYCMatch]
      } else Array.empty[BestKYCMatch]

      val unifiedScoredMatchesKyc = if(enableUnifiedEntitySource) {
        if (kycRequested) metrics.time("resultmatcher", "q_type:kyc", "vendor:unified_entity")(resultMatcher.calculateMatches(searchRequest, unifiedIdentityResultKyc, entity = false).right.get)
        else Array.empty[BestKYCMatch]
      } else Array.empty[BestKYCMatch]

      val scoredMatchesEntity = {
        if (entityRequested) {
          if (resolvedReq.samePreferences() && kycRequested) {
            metrics.increment("preferences.samePref", tags = "kycPrefSSN:" + resolvedReq.preferencesKyc.exactSSN)
            scoredMatchesKyc
          }
          else metrics.time("resultmatcher", "q_type:entity")(resultMatcher.calculateMatches(searchRequest, identityResultEntity, entity = true).right.get)
        } else Array.empty[BestKYCMatch]
      }

      val enformionScoredMatchesEntity = if (enformionMergeEnabled) {
        if (entityRequested) {
          if (resolvedReq.samePreferences() && kycRequested) {
            enformionScoredMatchesKyc
          }
          else metrics.time("resultmatcher", "q_type:entity", "vendor:enformion")(resultMatcher.calculateMatches(searchRequest, enformionIdentityResultEntity, entity = true).right.get)
        } else Array.empty[BestKYCMatch]
      } else Array.empty[BestKYCMatch]

      val unifiedScoredMatchesEntity = if (enableUnifiedEntitySource && entityRequested) {
        if (resolvedReq.samePreferences() && kycRequested) {
          unifiedScoredMatchesKyc
        }
        else metrics.time("resultmatcher", "q_type:entity", "vendor:unified_entity")(resultMatcher.calculateMatches(searchRequest, unifiedIdentityResultEntity, entity = true).right.get)
      } else Array.empty[BestKYCMatch]

      val top20EquifaxEntities = getTopNIdentityRecords(if (kycRequested) Some(scoredMatchesKyc) else if (entityRequested) Some(scoredMatchesEntity) else None)
      val top20EnformionEntities = getTopNIdentityRecordsByCluster(if (kycRequested) Some(enformionScoredMatchesKyc) else if (entityRequested) Some(enformionScoredMatchesEntity) else None)

      /** Computing best matches */
      val bestOfTwoSourcesKyc = {
        val bestKYCMatch = if (enformionMergeEnabled && kycRequested) {

          val filteredEnformionScoredMatchesKyc = enformionScoredMatchesKyc.filterNot { kycMatch =>
            kycMatch.cluster.getDataSource.exists(dataSource => {
              dataSourcesToSkipForBaseEntity.contains(dataSource)
            })
          }
          metrics.count("equifax.records.count", scoredMatchesKyc.size, tags = "q_type:kyc")
          metrics.count("enformion.records.count", enformionScoredMatchesKyc.size, tags = "q_type:kyc")
          scoredMatchesKyc.headOption match {
            case Some(efxBestMatch) => filteredEnformionScoredMatchesKyc.headOption match {
              case Some(enfBestMatch) =>
                val comparison = resultMatcher.weightCompare(enfBestMatch, efxBestMatch, isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequest)
                if (comparison > 0)
                  Some(enfBestMatch)
                else {
                  if (comparison == 0) {
                    metrics.increment("equal.weightage", tags = "kyc")
                  }
                  Some(efxBestMatch)
                } //on equal criteria, we need to return efx by default (metrics to show count of equal weight will be removed post analysis of prod metrics)
              //count of records, for equifax query and enf query
              case None => Some(efxBestMatch)
            }
            case None => filteredEnformionScoredMatchesKyc.headOption match {
              case Some(enfBestMatch) => Some(enfBestMatch)
              case None => None
            }
          }
        } else None
        bestKYCMatch
      }

      val bestOfTwoSourcesEntity = {
        val bestKYCMatch = if (enformionMergeEnabled && entityRequested && !(resolvedReq.samePreferences() && kycRequested)) {
          val filteredEnformionScoredMatchesEntity = enformionScoredMatchesEntity.filterNot { kycMatch =>
            kycMatch.cluster.getDataSource.exists(dataSource => {
              dataSourcesToSkipForBaseEntity.contains(dataSource)
            })
          }
          metrics.count("equifax.records.count", scoredMatchesEntity.size, tags = "q_type:entity")
          metrics.count("enformion.records.count", enformionScoredMatchesEntity.size, tags = "q_type:entity")
          scoredMatchesEntity.headOption match {
            case Some(efxBestMatch) => filteredEnformionScoredMatchesEntity.headOption match {
              case Some(enfBestMatch) =>
                val comparison = resultMatcher.weightCompare(enfBestMatch, efxBestMatch, isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequest)
                if (comparison > 0)
                  Some(enfBestMatch)
                else {
                  if (comparison == 0) {
                    metrics.increment("equal.weightage", tags = "entity")
                  }
                  Some(efxBestMatch)
                } //on equal criteria, we need to return efx by default (metrics to show count of equal weight will be removed post analysis of prod metrics)
              //count of records, for equifax query and enf query
              case None => Some(efxBestMatch)
            }
            case None => filteredEnformionScoredMatchesEntity.headOption match {
              case Some(enfBestMatch) => Some(enfBestMatch)
              case None => None
            }
          }
        } else bestOfTwoSourcesKyc
        bestKYCMatch
      }

      if(bestOfTwoSourcesKyc.nonEmpty && searchRequest.nationalIdResolved.nonEmpty) {
        val resolved4to9NationalId = getResolved9DigitSSN(
          searchRequest.nationalIdResolved.get.cleaned,
          bestOfTwoSourcesKyc.get
        ).value
        if(!resolved4to9NationalId.equals(searchRequest.nationalIdResolved.get.cleaned.value)) {
          ecbsvInHouseDataKycTransactionProcess.update4to9ResolvedSSN(resolved4to9NationalId)
        }
      }

      val allClusterIdsKyc = (scoredMatchesKyc ++ enformionScoredMatchesKyc).flatMap(_.cluster.clusterId).toSet
      val allClusterIdsEntity = (scoredMatchesEntity ++ enformionScoredMatchesEntity).flatMap(_.cluster.clusterId).toSet
      val baseEntityClusterIdKyc = bestOfTwoSourcesKyc.flatMap(_.cluster.clusterId)
      val baseEntityClusterIdEntity = bestOfTwoSourcesEntity.flatMap(_.cluster.clusterId)
      val additionalMatchesKycFuture = metrics.timeFuture("fetch.additional_matching_entities", "workflow:kyc") {
        fetchAdditionalMatchingEntities(searchRequest, baseEntityClusterIdKyc.getOrElse(""), allClusterIdsKyc, dataSourcesToQueryServiceMap, mockDataId, entity = false)
      }
      val additionalMatchesEntityFuture = if (!baseEntityClusterIdKyc.getOrElse("").equals(baseEntityClusterIdEntity.getOrElse(""))) {
        metrics.timeFuture("fetch.additional_matching_entities", "workflow:entity") {
          fetchAdditionalMatchingEntities(searchRequest, baseEntityClusterIdEntity.getOrElse(""), allClusterIdsEntity, dataSourcesToQueryServiceMap, mockDataId, entity = true)
        }
      } else additionalMatchesKycFuture
      val ecbsvInhouseFuture = ecbsvInHouseDataKycTransactionProcess.generateInhouseTransformedFuture(searchRequest)
      val resolvedRuleCodes = for {
        (additionalMatchesKyc, additionalScoredMatchesKyc) <- additionalMatchesKycFuture
        mergedNationalIdResultsAsBestKycMatch = Some(nationalIdResult.appended(enformionNationalIdResult)).map(i => i.attributes.zip(i.identityRecords.getOrElse(Seq.empty)))
          .map(j => j.map(
            i => {
              BestKYCMatch(
                cluster = i._1,
                piiMatchResults = Map.empty,
                bestMatchedElements = null,
                mergeStatus = None,
                identityRecord = Some(i._2),
                hasFirstNamePartialMatch = None,
                hasSurNamePartialMatch = None
              )
            })
          ).get
        (additionalMatchesEntity, additionalScoredMatchesEntity) <- additionalMatchesEntityFuture
        (ecbsvMatchList, ecbsvBestMatchOpt) <- ecbsvInhouseFuture
        bestOfThreeSourcesKYCFuture = ((bestOfTwoSourcesKyc, ecbsvBestMatchOpt) match {
          case (None, None) => None
          case (Some(a), None) => Some(a)
          case (None, Some(b)) => Some(b.getBestKYCMatch())
          case (Some(a), Some(b)) =>
            if (isSinglePiiRequest && searchRequest.processingMetadata.inputPiiCount == 1 && resolvedReq.nationalId.exists(_.value.nonEmpty)) {
              //only ssn is given in the request
              val closestMatchedWithEcbsv = (scoredMatchesKyc ++ enformionScoredMatchesKyc).find{entity => MergedEntityUtil.validateMergeLogic(entity, b.getBestKYCMatch(), entity, accept2PII = true)}
              closestMatchedWithEcbsv.orElse(bestOfTwoSourcesKyc)
            } else {
              if (resultMatcher.weightCompare(a, b.getBestKYCMatch(), isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequest) >= 0) Some(a)
              else Some(b.getBestKYCMatch())
            }
        }).map { entity =>
          val hitsMap = (scoredMatchesKyc ++ enformionScoredMatchesKyc ++ ecbsvMatchList.map(_.getBestKYCMatch())).flatMap { entity =>
            val dataSource = findDataSource(entity.cluster.clusterId)
            dataSource.map((_, entity))
          }.groupBy(_._1).mapValues(_.map(_._2))
          mergeEntities(mergeEntityConfig, "kyc", searchRequest, hitsMap, entity, socureIDClient, additionalScoredMatchesKyc, mergedNationalIdResultsAsBestKycMatch, nationalIdRefiner).map(Option(_))
        }.getOrElse(Future.successful(None))
        bestOfThreeSourcesEntityFuture = ((bestOfTwoSourcesEntity, ecbsvBestMatchOpt) match {
          case (None, None) => None
          case (Some(a), None) => Some(a)
          case (None, Some(b)) => Some(b.getBestKYCMatch())
          case (Some(a), Some(b)) => {
            if (isSinglePiiRequest && searchRequest.processingMetadata.inputPiiCount == 1 && resolvedReq.nationalId.exists(_.value.nonEmpty)) {
              //only ssn is given in the request
              val closestMatchedWithEcbsv = (scoredMatchesEntity ++ enformionScoredMatchesKyc).find { entity => MergedEntityUtil.validateMergeLogic(entity, b.getBestKYCMatch(), entity, accept2PII = true) }
              closestMatchedWithEcbsv.orElse(bestOfTwoSourcesEntity)
            } else {
              if (resultMatcher.weightCompare(a, b.getBestKYCMatch(), isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequest) >= 0) Some(a)
              else Some(b.getBestKYCMatch())
            }
          }
        }).map { entity =>
          val hitsMap = (scoredMatchesEntity ++ enformionScoredMatchesEntity ++ ecbsvMatchList.map(_.getBestKYCMatch())).flatMap { entity =>
            val dataSource = findDataSource(entity.cluster.clusterId)
            dataSource.map((_, entity))
          }.groupBy(_._1).mapValues(_.map(_._2))
          mergeEntities(mergeEntityConfig, "entity", searchRequest, hitsMap, entity, socureIDClient, additionalScoredMatchesEntity, mergedNationalIdResultsAsBestKycMatch, nationalIdRefiner).map(Option(_))
        }.getOrElse(Future.successful(None))
        bestOfThreeSourcesKYCWithoutEmailEnrichment <- bestOfThreeSourcesKYCFuture
        bestOfThreeSourcesEntity <- bestOfThreeSourcesEntityFuture
        mergedNationalIdResultsFuture = getResolvedNineDigitNationalIDResults(
          searchRequest,
          bestOfThreeSourcesKYCWithoutEmailEnrichment.orElse(bestOfThreeSourcesEntity),
          nationalIdResult.appended(
            enformionNationalIdResult,
            Elastic4sResult(
              if (bestOfThreeSourcesKYCWithoutEmailEnrichment.orElse(bestOfThreeSourcesEntity).exists(_.cluster.clusterId.exists(_.startsWith("EID")))) ecbsvMatchList.map(_.cluster)
              else Array.empty
            )
          ),
          eqxQueryService,
          enfQueryService,
          enformionMergeEnabled,
          mockDataId
        )
        mergedNationalIdResults <- mergedNationalIdResultsFuture
        isBMEHasNonCommercialAddressFuture = bestOfThreeSourcesKYCWithoutEmailEnrichment.map { bestMatchedEntity =>
            addressAnalyzer.isBMEHasNonCommercialAddress(
              accountId = resolvedReq.accountId.getOrElse(0L),
              inputStreet = resolvedReq.streetAddress.map(_.value).getOrElse(""),
              addressComponents = resolvedReq.addressComponents,
              bmeAddressList = bestMatchedEntity.identityRecord.map(_.address).getOrElse(Seq.empty)
            )
        }.getOrElse(Future.successful(false))
        anchorRecordsBasedOnInputFuture = if (socureIDClient.isEmpty) {
          getAnchorDeceasedRecordUsingInputSSN(searchRequest, bestOfThreeSourcesKYCWithoutEmailEnrichment)
        } else {
          Future.successful((Seq.empty[AnchorDeceasedRecord], Option.empty[AnchorDeceasedRecord]))
        }
        clusterIdsKYC = extractClusterIds(bestOfThreeSourcesKYCWithoutEmailEnrichment.flatMap(_.cluster.clusterId))
        clusterIdsEntity = extractClusterIds(bestOfThreeSourcesEntity.flatMap(_.cluster.clusterId))
        isKycAndEntityBMESame = clusterIdsEntity.toSet.intersect(clusterIdsKYC.toSet).nonEmpty
        uniqueEntityIdFuture = if (kycRequested && !isHealthCheck)
          uniqueEntityIdHandler.generateUniqueEntityId(
            searchRequest,
            bestOfThreeSourcesKYCWithoutEmailEnrichment,
            bestMatchedInternalEntity,
            None,
            "kyc",
            updateAdditionalPII = true,
            isTimedOut,
            internalEntityDataHandler.isFeatureEnabled
          ) else Future.successful(UniqueEntityIdResult())

        uniqueEntityIdFutureEntity = if (entityUnificationDao != null && entityRequested && bestOfThreeSourcesEntity.isDefined && isDeceasedCheckRequested && !isKycAndEntityBMESame && !isHealthCheck) {
          uniqueEntityIdFuture.flatMap { uniqueEntityIdResult =>
            uniqueEntityIdHandler.generateUniqueEntityId(
              searchRequest,
              bestOfThreeSourcesEntity,
              bestMatchedInternalEntity,
              uniqueEntityIdResult.internalEntityClusterId,
              "entity",
              updateAdditionalPII = !kycRequested,
              isTimedOut,
              internalEntityDataHandler.isFeatureEnabled)
          }
        } else uniqueEntityIdFuture
        bestOfThreeSourcesKYCFutureWithEmailEnrichment = metrics.timeFuture("email.processing.duration") { bestOfThreeSourcesKYCWithoutEmailEnrichment.map { bestKYCMatch =>
          emailProcessingService.processEmail(bestKYCMatch).recover {
            case ex: Exception =>
              logger.error("Unexpected failure in processEmail", ex)
              metrics.increment("email.processing.exception")
              Some(bestKYCMatch)
          }
        }.getOrElse(Future.successful(None))}
        anchorRecordsBasedOnInput <- anchorRecordsBasedOnInputFuture
        isBMEHasNonCommercialAddress <- isBMEHasNonCommercialAddressFuture
        obitRecordBasedOnPIIs <- Future.sequence(deceasedFutures)
        bestOfThreeSourcesKYC <- bestOfThreeSourcesKYCFutureWithEmailEnrichment
        anchorRecordUsingInputSSN = anchorRecordsBasedOnInput._2
        mergedMatches = scoredMatchesKyc ++ enformionScoredMatchesKyc ++ ecbsvMatchList.map(_.getBestKYCMatch()) ++ additionalScoredMatchesKyc
        hitMap = constructVendorBasedMatch(mergedMatches)
        syntheticMergedMatchesMap:Map[DataSources.Value, Array[BestKYCMatch]] = filterInfForEquifax(hitMap)

        syntheticResultOpt = syntheticBmePriority.collectFirst {
          case key if syntheticMergedMatchesMap.get(key).exists(_.nonEmpty) &&  hasNonEmptyRowIds(key, syntheticMergedMatchesMap) => key
        }

        rawBestMatchSynthetic <- syntheticResultOpt match {
          case Some(key) =>
            val matches = syntheticMergedMatchesMap(key)
            val syntheticMatchcedMap = Map(key -> matches)
            mergeEntities(mergeEntityConfig, "synthetic", searchRequest, syntheticMatchcedMap, matches.head, socureIDClient, Array.empty, mergedNationalIdResultsAsBestKycMatch, nationalIdRefiner)
              .map(bme => loadData(Some(bme), resolvedReq, is4DigitSsn,featureFlags = featureFlags))
          case None =>
            Future.successful(loadData(None, resolvedReq, is4DigitSsn,featureFlags = featureFlags))
        }

        uniqueEntityIdResult <- uniqueEntityIdFuture
        mergedMatchesEntity = scoredMatchesEntity ++ enformionScoredMatchesEntity ++ ecbsvMatchList.map(_.getBestKYCMatch()) ++ additionalScoredMatchesEntity

        //socure id generation for entity
        uniqueEntityIdResultForEntity <- uniqueEntityIdFutureEntity

        /** Merging results */

        mergedKYCResults = if (kycRequested) identityResultKyc.appended(enformionIdentityResultKyc, Elastic4sResult(ecbsvMatchList.map(_.cluster)), additionalMatchesKyc)
        else Elastic4sResult.empty

        mergedEntityResults = {
          if (resolvedReq.samePreferences() && kycRequested) mergedKYCResults
          else if (entityRequested) identityResultEntity.appended(enformionIdentityResultEntity, Elastic4sResult(ecbsvMatchList.map(_.cluster)), additionalMatchesEntity)
          else Elastic4sResult.empty
        }

        bestOfEqxAndInhouseKYC: Option[BestKYCMatch] = {
          (scoredMatchesKyc.headOption, ecbsvBestMatchOpt) match {
            case (None, None) => None
            case (Some(a), None) => Some(a)
            case (None, Some(b)) => Some(b.getBestKYCMatch())
            case (Some(a), Some(b)) =>
              if (resultMatcher.weightCompare(a, b.getBestKYCMatch(), isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequest) >= 0) Some(a)
              else Some(b.getBestKYCMatch())
          }
        }

        bestOfEqxAndInhouseEntity: Option[BestKYCMatch] = {
          (scoredMatchesEntity.headOption, ecbsvBestMatchOpt) match {
            case (None, None) => None
            case (Some(a), None) => Some(a)
            case (None, Some(b)) => Some(b.getBestKYCMatch())
            case (Some(a), Some(b)) =>
              if (resultMatcher.weightCompare(a, b.getBestKYCMatch(), isDecisionEnabled, isSinglePiiRequest, is4DigitSsn, searchRequest) >= 0) Some(a)
              else Some(b.getBestKYCMatch())
          }
        }

        bestMatchEntityMultiSource = if (enformionMergeEnabled) {
          if (kycRequested) loadData(bestOfThreeSourcesKYC, searchRequest.resolvedReq, is4DigitSsn,featureFlags = featureFlags)
          else if (entityRequested) loadData(bestOfThreeSourcesEntity, searchRequest.resolvedReq, is4DigitSsn,featureFlags = featureFlags)
          else None
        } else {
          if (kycRequested) {
            populateBestMatchData(bestOfEqxAndInhouseKYC.map(Array.apply(_)), searchRequest.resolvedReq,featureFlags = featureFlags)
          }
          else if (entityRequested) {
            populateBestMatchData(bestOfEqxAndInhouseEntity.map(Array.apply(_)), searchRequest.resolvedReq,featureFlags = featureFlags)
          }
          else None
        }

        //KYC
        bestMatchEntityMultiSourceForPrefill = if (enformionMergeEnabled) {
          if (kycRequested) loadData(bestOfThreeSourcesKYC, searchRequest.resolvedReq, true, is4DigitSsn,featureFlags = featureFlags)
          else if (entityRequested) loadData(bestOfThreeSourcesEntity, searchRequest.resolvedReq, true, is4DigitSsn,featureFlags = featureFlags)
          else None
        } else {
          if (kycRequested) populateBestMatchData(bestOfEqxAndInhouseKYC.map(Array.apply(_)), searchRequest.resolvedReq, true,featureFlags = featureFlags)
          else if (entityRequested) populateBestMatchData(bestOfEqxAndInhouseEntity.map(Array.apply(_)), searchRequest.resolvedReq, true,featureFlags = featureFlags)
          else None
        }

        //ENTITY
        bestMatchEntityMultiSourcePrefillForEntity = if (enformionMergeEnabled) {
          if (entityRequested) loadData(bestOfThreeSourcesEntity, searchRequest.resolvedReq, isPrefill = true, is4DigitSsn,featureFlags = featureFlags) else None
        } else {
          if (entityRequested) populateBestMatchData(bestOfEqxAndInhouseEntity.map(Array.apply(_)), searchRequest.resolvedReq, isPrefill = true,featureFlags = featureFlags) else None
        }

        bestOfThreeSources = if (enformionMergeEnabled) {
          if (kycRequested) bestOfThreeSourcesKYC
          else if (entityRequested) bestOfThreeSourcesEntity
          else None
        } else {
          if (kycRequested) bestOfEqxAndInhouseKYC
          else if (entityRequested) bestOfEqxAndInhouseEntity
          else None
        }

        deceasedDateFromBestOfThreeSourcesKYC: Option[String] = (bestOfThreeSourcesKYC) match {
          case Some(bme) => getDeceasedDate(bme)
          case _ => None
        }

        deceasedDateKYCWithAnchor: Option[String] = (deceasedDateFromBestOfThreeSourcesKYC) match {
          case Some(date) => deceasedDateFromBestOfThreeSourcesKYC
          case _ => (bestOfThreeSourcesKYC, anchorRecordUsingInputSSN) match {
            case (Some(bme), Some(anchorBme)) if hasMatch(bme, anchorBme) => AnchorLookupUtil.getDeceasedDateFromAnchorRecord(Some(anchorBme))
            case _ => None
          }
        }
        obitData = {
          deceasedRecordService.processObitData(searchRequest, obitRecordBasedOnPIIs, kycRequested, entityRequested, bestOfThreeSourcesKYC, hitMap)
        }
        scoredObitMatchesKyc = obitData.scoredObitMatchesKyc
        scoredObitMatchesEntity = obitData.scoredObitMatchesEntity
        top20obitRecords =  obitData.top20obitRecords
        obitDeceasedDate = obitData.obitDeceasedDate
        obitClusterId = obitData.clusterId

        deceasedDateKYC: Option[String] = (deceasedDateKYCWithAnchor) match {
          case Some(date) => Some(date)
          case _ => (bestOfThreeSourcesKYC, scoredObitMatchesKyc.headOption) match {
            case (Some(bme), Some(obitMatch)) => obitDeceasedDate
            case _ => None
          }
        }


        bestMatchEntityMultiSourceForKycPlus: Option[BestMatchEntity] =
          if (isKycPlusEnabled)
            transformBMEForCustomer(
              searchRequest = resolvedReq,
              originalBMEOpt = bestMatchEntityMultiSourceForPrefill.map(_.copy(deceasedDate = deceasedDateKYC)),
              bestKYCMatch = bestOfThreeSources,
              hitMap = hitMap
            )
          else None

        deceasedDateEntityWithAnchor: Option[String] = bestOfThreeSourcesEntity.flatMap(bestOfThreeSources => getDeceasedDate(bestOfThreeSources)).orElse {
          (bestOfThreeSourcesEntity, anchorRecordUsingInputSSN) match {
            case (Some(bme), Some(anchorBme)) if hasMatch(bme, anchorBme) => AnchorLookupUtil.getDeceasedDateFromAnchorRecord(Some(anchorBme))
            case _ => None
          }
        }

        obitDeceasedDateEntity = getObitMatch(bestOfThreeSourcesEntity, scoredObitMatchesEntity, hitMap, "entity")
        deceasedDateEntity: Option[String] = (deceasedDateEntityWithAnchor) match {
          case Some(date) => Some(date)
          case _ => (bestOfThreeSourcesEntity, scoredObitMatchesEntity.headOption) match {
            case (Some(bme), Some(obitMatch)) => {
              metrics.increment("entity.deceasedonlybyobit."+"entity")
              obitDeceasedDateEntity
            }
            case _ => None
          }
        }

        equifaxEntitiesInMergedEntity = GenericHelper.getEquifaxEntitiesForMergedEntity(
          mergedEntity = bestOfThreeSourcesKYC.getOrElse(BestKYCMatch(Records(), Map.empty)),
          scoredMatchedKyc = mergedMatches
        )

        equifaxEntitiesInMergedEntityEntity = {
          if (resolvedReq.samePreferences() && kycRequested) {
            equifaxEntitiesInMergedEntity
          } else {
            GenericHelper.getEquifaxEntitiesForMergedEntity(
              mergedEntity = bestOfThreeSourcesEntity.getOrElse(BestKYCMatch(Records(), Map.empty)),
              scoredMatchedKyc = mergedMatchesEntity
            )
          }
        }

        /** Resolving numerical and categorical codes */
        numericalRuleCodesFuture = {
          if (kycRequested) metrics.timeFuture("rulecode.generator.numerical", "q_type:kyc")(
            KYCRuleCodeFactory.resolve(
              ComputedResultsDTO(
                searchRequest = searchRequest,
                foundIdentityResults = mergedKYCResults,
                foundNationalIdResults = mergedNationalIdResults,
                scoredMatches = mergedMatches,
                equifaxBestMatch = scoredMatchesKyc.head,
                bestOfTwoSources = bestOfThreeSourcesKYC,
                anchorRecordUsingInputSSN = anchorRecordUsingInputSSN,
                anchorRecordUsingBME = None,
                syntheticBestKYCMatch = rawBestMatchSynthetic,
                bestKYCMatch = bestMatchEntityMultiSourceForPrefill,
                isDeceasedViaScrappedEntity = obitDeceasedDate,
                obitClusterId = obitClusterId,
                equifaxEntitiesInMergedEntity = equifaxEntitiesInMergedEntity
              )
            )
          )
          else Future.successful(Map.empty[String, Double])
        }.map(_ ++ Map(RuleCodes.EX_MISSING_UNIT_BME_NON_COMMERCIAL.code.toString -> (if (isBMEHasNonCommercialAddress) 1.0 else 0.0))).map(
          _ ++ generateUniqueRecordRuleCode(scoredMatchesKyc, hitMap)
        )

        categoricalRuleCodesFuture = {
          if (kycRequested) metrics.timeFuture("rulecode.generator.categorical", "q_type:kyc")(
            KYCRuleCodeFactory.resolveCategorical(
              ComputedResultsDTO(
                searchRequest = searchRequest,
                foundIdentityResults = mergedKYCResults,
                foundNationalIdResults = mergedNationalIdResults,
                scoredMatches = mergedMatches,
                equifaxBestMatch = scoredMatchesKyc.head,
                bestOfTwoSources = bestOfThreeSourcesKYC,
                anchorRecordUsingInputSSN = anchorRecordUsingInputSSN,
                anchorRecordUsingBME = None,
                syntheticBestKYCMatch = rawBestMatchSynthetic,
                bestKYCMatch = bestMatchEntityMultiSourceForPrefill,
                isDeceasedViaScrappedEntity = obitDeceasedDate,
                obitClusterId = obitClusterId,
                equifaxEntitiesInMergedEntity = equifaxEntitiesInMergedEntity
              )
            )
          )
          else Future.successful(Map.empty[String, String])
        }.map(_ ++ uniqueEntityIdHandler.generateRuleCodes(uniqueEntityIdResult))

        numericalRuleCodesFutureEntity = {
          if (entityRequested) {
            if (resolvedReq.samePreferences() && kycRequested) {
              metrics.increment("entity.rulecode.generator.samePref")
              numericalRuleCodesFuture
            }
            else
              metrics.timeFuture("entity.rulecode.generator")(
                SigmaRuleCodeFactory.resolve(
                  ComputedResultsDTO(
                    searchRequest = searchRequest,
                    foundIdentityResults = mergedEntityResults,
                    foundNationalIdResults = mergedNationalIdResults,
                    scoredMatches = mergedMatchesEntity,
                    equifaxBestMatch = scoredMatchesEntity.head,
                    bestOfTwoSources = bestOfThreeSourcesEntity,
                    anchorRecordUsingInputSSN = anchorRecordUsingInputSSN,
                    anchorRecordUsingBME = None,
                    syntheticBestKYCMatch = rawBestMatchSynthetic,
                    bestKYCMatch = bestMatchEntityMultiSourceForPrefill,
                    isDeceasedViaScrappedEntity = obitDeceasedDate,
                    obitClusterId = obitClusterId,
                    equifaxEntitiesInMergedEntity = equifaxEntitiesInMergedEntityEntity
                  )
                )
              )
          }
          else Future.successful(Map.empty[String, Double])
        }.map(_ ++ Map(RuleCodes.EXX_MISSING_UNIT_BME_NON_COMMERCIAL.code.toString -> (if (isBMEHasNonCommercialAddress) 1.0 else 0.0))).map(
          _ ++ generateUniqueRecordRuleCode(scoredMatchesEntity, hitMap)
        )

        categoricalRuleCodesFutureEntity = {
          if (entityRequested) {
            if (resolvedReq.samePreferences() && kycRequested) {
              categoricalRuleCodesFuture
            }
            else
              metrics.timeFuture("entity.rulecode.categorical.generator")(
                SigmaRuleCodeFactory.resolveCategorical(
                  ComputedResultsDTO(
                    searchRequest = searchRequest,
                    foundIdentityResults = mergedEntityResults,
                    foundNationalIdResults = mergedNationalIdResults,
                    scoredMatches = mergedMatchesEntity,
                    equifaxBestMatch = scoredMatchesEntity.head,
                    bestOfTwoSources = bestOfThreeSourcesEntity,
                    anchorRecordUsingInputSSN = anchorRecordUsingInputSSN,
                    anchorRecordUsingBME = None,
                    syntheticBestKYCMatch = rawBestMatchSynthetic,
                    bestKYCMatch = bestMatchEntityMultiSourceForPrefill,
                    isDeceasedViaScrappedEntity = obitDeceasedDate,
                    obitClusterId = obitClusterId,
                    equifaxEntitiesInMergedEntity = equifaxEntitiesInMergedEntityEntity
                  )
                )
              )
          }
          else Future.successful(Map.empty[String, String])
        }.map(_ ++ uniqueEntityIdHandler.generateRuleCodes(uniqueEntityIdResultForEntity))

        resolvedNumericalCodesKyc <- numericalRuleCodesFuture
        resolvedCategoricalKyc <- categoricalRuleCodesFuture
        resolvedNumericalCodesEntity <- numericalRuleCodesFutureEntity
        resolvedCategoricalEntity <- categoricalRuleCodesFutureEntity
      } yield {

        logAnchorMetrics (mergedNationalIdResults, anchorRecordUsingInputSSN, searchRequest, accountId)

        /** Additional params - to be removed before prod deployment */
        val efxBestMatch = {
          if (kycRequested) populateBestMatchData(Some(scoredMatchesKyc), searchRequest.resolvedReq,featureFlags = featureFlags)
          else if (entityRequested) populateBestMatchData(Some(scoredMatchesEntity), searchRequest.resolvedReq,featureFlags = featureFlags)
          else None
        }

        val enfBestMatch = {
          if (kycRequested) populateBestMatchData(Some(enformionScoredMatchesKyc), searchRequest.resolvedReq,featureFlags = featureFlags)
          else if (entityRequested) populateBestMatchData(Some(enformionScoredMatchesEntity), searchRequest.resolvedReq,featureFlags = featureFlags)
          else None
        }

        val unifiedBestMatch = {
          if (kycRequested) populateBestMatchData(Some(unifiedScoredMatchesKyc), searchRequest.resolvedReq,featureFlags = featureFlags)
          else if (entityRequested) populateBestMatchData(Some(unifiedScoredMatchesEntity), searchRequest.resolvedReq,featureFlags = featureFlags)
          else None
        }

        val anchorSourceVerificationCodes = {
          val ssnDeceased = resolvedNumericalCodesKyc.get(RuleCodes.EX_ANC_SSN_DECEASED.code.toString).contains(1.0)
          val identityDeceased = resolvedNumericalCodesKyc.get(RuleCodes.EX_ANC_IDENTITY_ISDECEASED.code.toString).contains(1.0)

          List(
            if (ssnDeceased || identityDeceased) anchorRecordUsingInputSSN.flatMap(_.sourceVerificationCode)
            else None
          ).flatten
        }

        val anchorSourceVerificationCodesForEntity = {
          val ssnDeceased = resolvedNumericalCodesEntity.get(RuleCodes.EXX_ANC_SSN_DECEASED.code.toString).contains(1.0)
          val identityDeceased = resolvedNumericalCodesEntity.get(RuleCodes.EXX_ANC_IDENTITY_ISDECEASED.code.toString).contains(1.0)

          List(
            if (ssnDeceased || identityDeceased) anchorRecordUsingInputSSN.flatMap(_.sourceVerificationCode)
            else None
          ).flatten
        }

        val bestMatchEntityForDeceasedCheck: Option[BestMatchEntity] =
          if(isDeceasedCheckRequested) {
            val cid = extractClusterIds(bestMatchEntityMultiSourcePrefillForEntity.flatMap(_.clusterId))

            val sourceAttributions = getSourceAttributions(getVendors(cid.toSet, anchorSourceVerificationCodesForEntity))

            transformBMEForCustomer(
              searchRequest = resolvedReq,
              originalBMEOpt = bestMatchEntityMultiSourcePrefillForEntity.map(_.copy(deceasedDate = deceasedDateEntity)),
              bestKYCMatch = bestOfThreeSourcesEntity,
              hitMap = hitMap,
              socureId = resolvedCategoricalEntity.get(RuleCodes.EXX_UNIQUE_ENTITY_ID.code.toString),
              sourceAttribution = Some(sourceAttributions)
              )
          }
          else None

        val cid = bestMatchEntityMultiSource.getOrElse(BestMatchEntity()).clusterId.toSet


        val maskedBestMatchEntityMultiSourceForPrefill = getUnsharedPIIMaskedBME(bestMatchEntityMultiSourceForPrefill, hitMap)

        KycEntityResponse(
          kyc = Features(
            numerical = resolvedNumericalCodesKyc,
            categorical = resolvedCategoricalKyc,
            cid = cid
          ),
          entity = Features(
            numerical = resolvedNumericalCodesEntity,
            categorical = resolvedCategoricalEntity
          ),
          bestMatchEntity = {
            if (kycRequested) getFilteredBestMatch(resolvedReq, mergedMatches,featureFlags = featureFlags)
            else if (entityRequested) getFilteredBestMatch(resolvedReq, mergedMatchesEntity,featureFlags = featureFlags)
            else None
          },
          kycRawBestMatchEntity = bestMatchEntityMultiSourceForPrefill,
          kycRawBestMatchEntityForPreFill = maskedBestMatchEntityMultiSourceForPrefill.map(_.copy(deceasedDate = deceasedDateKYC)),
          kycPlusBestMatchedEntity = bestMatchEntityMultiSourceForKycPlus.map(_.copy(deceasedDate = deceasedDateKYC)),
          equifaxBestMatchEntity = efxBestMatch,
          enformionBestMatchEntity = enfBestMatch,
          unifiedBestMatchEntity = unifiedBestMatch,
          foundInEfx = {
            if (efxBestMatch.exists(_.clusterId.nonEmpty)) metrics.increment("match.found.efx")
            efxBestMatch.map(_.clusterId.nonEmpty)
          },
          foundInEnf = {
            if (enfBestMatch.exists(_.clusterId.nonEmpty)) metrics.increment("match.found.enf")
            enfBestMatch.map(_.clusterId.nonEmpty)
          },
          foundInBoth = {
            if (efxBestMatch.exists(_.clusterId.nonEmpty) && enfBestMatch.exists(_.clusterId.nonEmpty)) metrics.increment("match.found.both")
            Some(efxBestMatch.exists(_.clusterId.nonEmpty) && enfBestMatch.exists(_.clusterId.nonEmpty))
          },
          foundInUnified = unifiedBestMatch.map(_.clusterId.nonEmpty),
          syntheticRawBestMatchEntity = rawBestMatchSynthetic,
          top20EquifaxEntities = top20EquifaxEntities,
          top20EnformionEntities = top20EnformionEntities,
          top20UnifiedEntities = populateTopNBestMatchData(if (kycRequested) Some(unifiedScoredMatchesKyc) else if (entityRequested) Some(unifiedScoredMatchesEntity) else None),
          top10eCBSVEntities = populateTopNBestMatchData(Some(ecbsvMatchList.take(10).map(_.getBestKYCMatch()))),
          anchorRecordUsingInputSSN = anchorRecordUsingInputSSN,
          top20obitRecords= top20obitRecords,
          top10AnchorRecordUsingInputSSN = Some(anchorRecordsBasedOnInput._1.take(10)),
          nationalIdQueryResults = mergedNationalIdResults.identityRecords,
          mergedEntity = if (mergeEntityConfig.enabled) {
            if (kycRequested) bestOfThreeSourcesKYC.flatMap(_.identityRecord) else bestOfThreeSourcesEntity.flatMap(_.identityRecord)
          } else None,
          mergedEntityStatus = if (kycRequested) bestOfThreeSourcesKYC.flatMap(_.mergeStatus) else bestOfThreeSourcesEntity.flatMap(_.mergeStatus),
          uniqueEntityId = resolvedCategoricalKyc.get(RuleCodes.EX_UNIQUE_ENTITY_ID.code.toString),
          derivedUniqueEntityId = resolvedCategoricalKyc.get(RuleCodes.EX_UNIQUE_ENTITY_ID.code.toString).map(uid => entityIdGenerator.encode(rootNonPartnerAccountId.getOrElse(accountId), uid)),
          sourceAttribution = Some(getSourceAttributions(getVendors(cid, anchorSourceVerificationCodes))),
          bestMatchEntityForDeceasedCheck = bestMatchEntityForDeceasedCheck,
          bestMatchedInternalEntity = bestMatchedInternalEntity.flatMap(_.identityRecord),
          additionalMatches = if (kycRequested) additionalMatchesKyc.identityRecords else additionalMatchesEntity.identityRecords
        )
      }

      resolvedRuleCodes
        .map(result => Right(result))
    }
    kycResponse.flatMap(result => result).recover {
      case ex: Exception =>
        trxLogger.error("Caught exception", ex)
        Left(ErrorResponses.internalError(ex))
    }
  }

  private def generateUniqueRecordRuleCode(scoredMatches: Array[BestKYCMatch], hitMap: Map[DataSources.Value, Array[BestKYCMatch]]) = {
    if (scoredMatches.nonEmpty) {
      val isUniqueRecordKyc = validateFrequencyDistributionForAddress(scoredMatches.head, hitMap) || (scoredMatches.head.piiMatchResults.get(DOBMatch).contains(1) && validateFrequencyDistribution(scoredMatches.head, hitMap))
      Map(EX_UNIQUE_ADDRESS_OR_DOB.code.toString -> boolToDouble(isUniqueRecordKyc))
    } else Map.empty
  }

  private def fetchAdditionalMatchingEntities(
                                               searchRequest: KYCSearchRequestResolved,
                                               clusterId: String,
                                               existingClusterIds: Set[String],
                                               dataSourceToQueryServiceMap: Map[DataSources.DataSource, QueryService],
                                               mockDataId: Long,
                                               entity: Boolean
                                             ): Future[(Elastic4sResult, Array[BestKYCMatch])] = {

    val associatedClusterIdsFuture = entityUnificationDao.getAssociatedClusterIds(clusterId)
    associatedClusterIdsFuture.flatMap { associatedClusterIds =>
      val futureResults = associatedClusterIds
        .filterNot(existingClusterIds.contains)
        .groupBy(clusterId => findVendorFromClusterID(Some(clusterId)))
        .flatMap {
          case (vendor, clusterIds) =>
            val idsQuery = QueryBuilders.idsQuery().addIds(clusterIds: _*)
            val query: String = idsQuery.toString
            DataSources.getDataSource(vendor).flatMap { dataSource =>
              dataSourceToQueryServiceMap.get(dataSource).map { queryService =>
                Try(queryService.execute(query, mockDataId, vendor, "IDENTITY_REQUEST"))
                  .getOrElse(Future.successful(Elastic4sResult.empty))
              }
            }
        }
      val future = Future.sequence(futureResults).map { results =>
        if (results.nonEmpty) {
          results.reduceLeft { (a: Elastic4sResult, b: Elastic4sResult) =>
            a.appended(b)
          }
        } else {
          Elastic4sResult.empty
        }
      }.map { searchResults =>
        val scoredMatches = resultMatcher.calculateMatches(searchRequest, searchResults, entity).right.getOrElse(Array.empty)
        (searchResults, scoredMatches)
      }
      .recover {
        case NonFatal(ex) =>
          logger.error("Error occurred in fetchAdditionalMatchingEntities", ex)
          (Elastic4sResult.empty, Array.empty[BestKYCMatch])
      }
      future.onSuccess {
        case (_, scoredMatches) =>
          if (scoredMatches.nonEmpty)
            metrics.increment("found_additional_matches", s"workflow:${if(!entity) "kyc" else "entity"}")
      }
      future
    }
  }

  private def extractClusterIds(clusterIdStr: Option[String]): Array[String] = {
    clusterIdStr.map(_.split(',').map(_.trim).distinct.filter(_.nonEmpty)).getOrElse(Array.empty)
  }

  private def transformBMEForCustomer(searchRequest: KycEntitySearchRequest,
                                      originalBMEOpt: Option[BestMatchEntity],
                                      bestKYCMatch: Option[BestKYCMatch],
                                      hitMap: Map[DataSources.Value, Array[BestKYCMatch]] = Map.empty,
                                      socureId: Option[String] = None,
                                      sourceAttribution: Option[List[String]] = None): Option[BestMatchEntity] = {

    val updatedOriginalBMEOpt = getUnsharedPIIMaskedBME(originalBMEOpt, hitMap)
    val normalizedAddressObject: Option[AddressObjectOption] = Some(AddressObjectOption(
      streetAddress = convertToProperCase(searchRequest.streetAddress.map(_.value)),
      city = convertToProperCase(searchRequest.city.map(_.value)),
      state = convertToUpperCase(searchRequest.state.map(_.value)),
      zipCode = formatZipCode(searchRequest.zipCode.map(_.value))
      ))

    updatedOriginalBMEOpt match {
      case Some(originalBME) =>
        if(bestKYCMatch.nonEmpty) {
          val rootAccountId = searchRequest.rootNonPartnerAccountId.orElse(searchRequest.accountId).getOrElse(-1L)
          val deceasedDate = originalBME.deceasedDate.flatMap(DateConversionFactory.toDate)
          val resolvedAge = originalBME.dob
                                       .filterNot(_.contains(PII_MASKED_VALUE))
                                       .flatMap(DOBResolverHelper.getResolvedAge(_, searchRequest.submissionDate, deceasedDate).map(_.toInt))
                                       .filter(age => 0 <= age && age <= 150)
          val bmeCriteria = validateKYCPlusBMECriteria(bestKYCMatch.get.piiMatchResults)
          Some(
            originalBME.copy(
              ssn = if (bmeCriteria) originalBME.ssn else None,
              ssnIssued = if (bmeCriteria) originalBME.ssnIssued else None,
              normalizedAddress = normalizedAddressObject,
              age = resolvedAge,
              sourceAttribution = sourceAttribution,
              socureId = socureId,
              derivedSocureId = socureId.map(entityIdGenerator.encode(rootAccountId, _))
            )
          )
        }
        else {
          Some(
            BestMatchEntity(
              normalizedAddress = normalizedAddressObject
              )
            )
        }
      case _ =>
        Some(
          BestMatchEntity(
            normalizedAddress = normalizedAddressObject
            )
          )
    }
  }

  private def removeDuplicates(entities: Array[BestKYCMatch]): Array[BestKYCMatch] = {
    entities.groupBy(_.cluster.clusterId.getOrElse("")).flatMap(_._2.headOption).toArray
  }

  private def mergeEntities(
                             mergeEntityConfig: MergeEntityConfig,
                             qType: String, requestResolved: KYCSearchRequestResolved,
                             hitsMap: Map[DataSources.DataSource, Array[BestKYCMatch]],
                             baseEntity: BestKYCMatch,
                             socureIDClient: Option[SocureIDClient],
                             additionalMatches: Array[BestKYCMatch],
                             nationalIdMatches: Array[BestKYCMatch],
                             nationalIdRefiner: NationalIdRefiner
                           )(implicit trxId: TrxId): Future[BestKYCMatch] = {
    val request = requestResolved.resolvedReq
    if(mergeEntityConfig.enabled && socureIDClient.isEmpty) {
      val accountId = request.accountId.getOrElse(-1L)
      val mergedEntityFuture = try {
        val future = Future {
          metrics.time("merge.entities.duration", s"q_type:$qType") {
            val isDecisionEnabled = request.modulesEnabled.contains("ModuleDecisioning")

            val additionalMatchesMap = additionalMatches.flatMap { entity =>
              val dataSource = findDataSource(entity.cluster.clusterId)
              dataSource.map((_, entity))
            }.groupBy(_._1).mapValues(_.map(_._2))


            val overallHitsMap = hitsMap.foldLeft(additionalMatchesMap) { case (acc, (key, value)) =>
                acc.get(key) match {
                  case Some(existingValue) =>
                    acc.updated(key, existingValue ++ value)
                  case None =>
                    acc + (key -> value)
                }
              }
              .mapValues(removeDuplicates)
              .mapValues(_.sorted(resultMatcher.BestKYCMatchOrdering(isDecisionEnabled, request.isSinglePiiRequest, requestResolved.processingMetadata.is4DigitSsn, requestResolved).reverse))

            val overallSortedHits = overallHitsMap
              .flatMap(_._2.take(mergeEntityConfig.vendorLimit)).toArray
              .filterNot(_.cluster.clusterId.contains(baseEntity.cluster.clusterId.getOrElse(""))) //remove the base entity from hits
              .sorted(resultMatcher.BestKYCMatchOrdering(isDecisionEnabled, request.isSinglePiiRequest, requestResolved.processingMetadata.is4DigitSsn, requestResolved).reverse)
              .take(mergeEntityConfig.overallLimit)

            MergedEntityUtil.mergeValidEntitiesForKYC(
              requestResolved,
              qType,
              overallSortedHits,
              baseEntity,
              overallHitsMap,
              resultMatcher,
              nationalIdMatches,
              nationalIdRefiner
            ) map  {
              mergedEntity =>
              val vendors = getVendorsFromClusterID(mergedEntity.cluster.clusterId)
              metrics.increment("merge.entities.vendors", vendors.map(vendor => s"vendor:$vendor") :+ s"q_type:$qType" :+ s"has_merge:${vendors.size > 1}": _*)
              if (qType.equals("kyc")) {
                Future(MergedEntityUtil.compareAndEmitMetrics(accountId, "ee_vs_me", Some(baseEntity), Some(mergedEntity), Seq(s"q_type:$qType"))) //emit metrics asynchronously
              }
              trxLogger.debug(s"Following clusterIds have been merged - ${mergedEntity.cluster.clusterId}")
              mergedEntity
            }
          }
        }.flatMap(i => i)
        FutureUtils.getFutureWithTimeout(future, Duration(mergeEntityConfig.timeLimitInMillis, TimeUnit.MILLISECONDS))
          .map {
            case Left(timeoutError) => throw new TimeoutException(timeoutError)
            case Right(mergedEntity) => mergedEntity
          }
      } catch {
        case ex: Throwable =>
          Future.failed(ex)
      }

      mergedEntityFuture.recover {
        case _: TimeoutException =>
          metrics.increment("merge.entities.timeout")
          trxLogger.error(s"Merge entity timed out after ${mergeEntityConfig.timeLimitInMillis} milliseconds")
          baseEntity.copy(mergeStatus = Some("Timeout"))
        case ex: Exception =>
          metrics.increment("merge.entities.failure", s"exception:${ex.getClass.getSimpleName}")
          trxLogger.error(s"Merge entity failed", ex)
          baseEntity.copy(mergeStatus = Some(s"Error:${ex.getMessage}"))
      }
    }
    else Future.successful(baseEntity)
  }


  def sendESResultsToSQS(accountId: Long,
                         transactionId: String,
                         transactionTimestamp: DateTime,
                         equifaxResults: EntitySearchResults,
                         enformionResults: EntitySearchResults)(implicit ec: ExecutionContext): Unit = {
    implicit val trxId: TrxId = TrxId(transactionId)
    try {
      val message = ResolvedEntityQueueMessage(
        messageType = MessageType.UnifiedEntity,
        data = UnifiedEntityQueueData(
          accountId = accountId,
          transactionId = transactionId,
          transactionTimestamp = transactionTimestamp,
          equifaxSearchResults = equifaxResults,
          enformionSearchResults = enformionResults
        )
      )
      val json = Serialization.write(message)
      val compressedString = compress(json)
      unifiedEntitySQSClient.send(
        transactionId, compressedString, s3FileName = Some(s"$transactionId.txt")
      ).onComplete {
        case Success(_) =>
          metrics.increment("es.results.send.sqs.success")
        case Failure(ex) =>
          logger.error("Error occurred when send ES Results to SQS", ex)
          metrics.increment("es.results.send.sqs.failure", s"class:${ex.getClass.getSimpleName}")
      }
    } catch {
      case ex: Throwable =>
        logger.error("Exception occurred when send ES Results to SQS", ex)
        metrics.increment("es.results.send.sqs.failure", s"class:${ex.getClass.getSimpleName}")
    }
  }

  def compress(data: String): String = {
    var bos: ByteArrayOutputStream = null
    var gzip: GZIPOutputStream = null
    try {
      bos = new ByteArrayOutputStream(data.length)
      gzip = new GZIPOutputStream(bos)
      gzip.write(data.getBytes)
      gzip.finish()
      Base64.getEncoder.encodeToString(bos.toByteArray)
    } catch {
      case ex: Exception =>
        logger.error("Compression failed", ex)
        data
    } finally {
      if (bos != null) bos.close()
      if (gzip != null) gzip.close()
    }
  }

  def toEntitySearchResults(data: Elastic4sResult): EntitySearchResults = {
    EntitySearchResults(
      records = data.attributes.map(record => recordsToExtendedRecords(record))
    )
  }





  def recordsToExtendedRecords(record: Records): EntityRecord = {
    EntityRecord(
      firstName = record.firstName,
      middleName = record.middleName,
      surName = record.surName,
      ssn = record.ssn,
      dob = record.dob,
      mobileNumber = record.mobileNumber,
      streetAddress = record.streetAddress,
      city = record.city,
      zipCode = record.zipCode,
      state = record.state,
      ssnDeceased = record.ssnDeceased,
      cidDeceased = record.cidDeceased,
      ssnYearHigh = record.ssnYearHigh,
      ssnYearLow = record.ssnYearLow,
      invalidSSN = record.invalidSSN,
      ssnConfirm = record.ssnConfirm,
      factaCode = record.factaCode,
      ciRowId = record.ciRowId,
      aRowId = record.aRowId,
      addressType = record.addressType,
      rowIds = record.rowIds,
      clusterId = record.clusterId,
      suffixName = record.suffixName
    )
  }



  def filterInfForEquifax(mergedMatchMap: Map[DataSources.Value, Array[BestKYCMatch]]): Map[DataSources.Value, Array[BestKYCMatch]] = {
    if (mergedMatchMap.contains(DataSources.Equifax)) {
      val originalArray = mergedMatchMap(DataSources.Equifax)
      var copiedArray = new Array[BestKYCMatch](originalArray.length)
      Array.copy(originalArray, 0, copiedArray, 0, originalArray.length)
      copiedArray = cleanInf(copiedArray)
      mergedMatchMap.updated(DataSources.Equifax, copiedArray)
    } else {
      mergedMatchMap
    }
  }

  private def cleanedPIIAndAddress(identityRecord: Option[IdentityRecord]): Option[IdentityRecord] = {
    identityRecord.map { record =>
      val updatedFirstName = removeLFMValues(record.firstName)
      val updatedSurName = removeLFMValues(record.surName)
      val updatedMiddleName = removeLFMValues(record.middleName)
      val updatedSuffixName = removeLFMValues(record.suffixName)
      val updatedDob = removeLFMValues(record.dob)
      val updatedPhoneNumber = removeLFMValues(record.phoneNumber)
      val updatedEmail = removeLFMValues(record.email)
      val updatedAddress = removeLFMValues(record.address)
      val updatedSSN = removeLFMValues(record.ssn)
      val updatedAllAssociatedSSNs = removeLFMValues(record.allAssociatedSSNs)

        // Return the updated IdentityRecord
      record.copy(
        firstName = updatedFirstName,
        surName = updatedSurName,
        middleName = updatedMiddleName,
        suffixName = updatedSuffixName,
        dob = updatedDob,
        phoneNumber = updatedPhoneNumber,
        email = updatedEmail,
        address = updatedAddress,
        ssn = updatedSSN,
        allAssociatedSSNs = updatedAllAssociatedSSNs
      )
    }
  }

  // Generalized method to filter and collect removed indices for any field type
  private def removeLFMValues[T <: { def rowId: Option[String] }](fields: Seq[T]): (Seq[T]) = {
    val validFields = scala.collection.mutable.ArrayBuffer[T]()

    fields.zipWithIndex.foreach { case (field, index) =>
      if (!field.rowId.getOrElse("").toUpperCase.contains("LFM")) {
        validFields += field
      }
    }
    validFields
  }


  private def fetchEmptyIdentityRecord(): IdentityRecord = {
    new IdentityRecord(Seq.empty, Seq.empty, Seq.empty, Seq.empty, Seq.empty, Seq.empty, Seq.empty, Seq.empty, Seq.empty, Seq.empty, IdentityRecordRemoved(), Seq.empty, None)
  }

  private def hasNonEmptyRowIds(key: DataSources.Value, mergedMatchesMap: Map[DataSources.Value, Array[BestKYCMatch]]): Boolean = {
    val matchOption: Option[Array[BestKYCMatch]] = Option(mergedMatchesMap.get(key)).flatten

    matchOption.flatMap(_.headOption) match {
      case Some(firstMatch: BestKYCMatch) =>
        Option(firstMatch.cluster) match {
          case Some(cluster) =>
            Option(cluster.rowIds) match {
              case Some(rowIds) => rowIds.nonEmpty
              case None => false // rowIds is null
            }
          case None => false // cluster is null
        }
      case None => false // matchOption is empty or null
    }
  }

  private def cleanInf(matches: Array[BestKYCMatch]): Array[BestKYCMatch] = {
    matches.map((element => {
      val updatedIdentityRecord  = cleanedPIIAndAddress(element.identityRecord)
      val record = updatedIdentityRecord.getOrElse(fetchEmptyIdentityRecord())
      val updatedCluster = element.cluster.copy(
        firstName = record.firstName.map(_.value).toArray,
        middleName = record.middleName.map(_.value).toArray,
        surName = record.surName.map(_.value).toArray,
        ssn = record.ssn.map(_.value).toArray,
        dob = record.dob.map(_.value).toArray,
        mobileNumber = record.phoneNumber.map(_.value).toArray,
        streetAddress = record.address.map(_.street).toArray,
        city = record.address.map(_.city).toArray,
        zipCode = record.address.map(_.zipCode).toArray,
        state = record.address.map(_.state).toArray,
        ssnDeceased = record.ssn.map(_.ssnDeceased.getOrElse("")).toArray,
        cidDeceased = record.ssn.map(_.cidDeceased.getOrElse("")).toArray,
        ssnYearHigh = record.ssn.map(_.ssnYearHigh.getOrElse("")).toArray,
        ssnYearLow = record.ssn.map(_.ssnYearLow.getOrElse("")).toArray,
        invalidSSN = record.ssn.map(_.invalid.getOrElse("")).toArray,
        ssnConfirm = record.ssn.map(_.ssnConfirm.getOrElse("")).toArray,
        factaCode = record.ssn.map(_.factaCode.getOrElse("")).toArray,
        ciRowId = record.ssn.map(_.rowId.getOrElse("")).toArray,
        aRowId = record.address.map(_.rowId.getOrElse("")).toArray,
        addressType = record.address.map(_.addressCommercial.getOrElse("")).toArray,
        rowIds = element.cluster.rowIds.filterNot(_.toUpperCase.contains("LFM")),
        clusterId = element.cluster.clusterId, // Keeping the same value
        suffixName = record.suffixName.map(_.value).toArray,
        phoneFirstSeen = record.phoneNumber.map(_.firstSeen.getOrElse("")).toArray,
        phoneLastSeen = record.phoneNumber.map(_.lastSeen.getOrElse("")).toArray,
        addressFirstSeen = record.address.map(_.firstSeen.getOrElse("")).toArray,
        addressLastSeen = record.address.map(_.lastSeen.getOrElse("")).toArray,
        deceasedDate = record.ssn.map(_.dod.getOrElse("")).toArray,
        piiRowIDs = PIIRowIDs(
          firstName = record.firstName.map(_.value).toArray,
          surName = record.middleName.map(_.value).toArray,
          dob = record.dob.map(_.value).toArray,
          mobileNumber = record.phoneNumber.map(_.value).toArray
        ),
        emailAddress = record.email.map(_.value).toArray,
        emailFirstSeen = record.email.map(_.firstSeen.getOrElse("")).toArray,
        emailLastSeen = record.email.map(_.lastSeen.getOrElse("")).toArray,
        ssnIssued = record.ssn.map(_.ssnYearLow.getOrElse("")).toArray
      )
      element.copy(cluster = updatedCluster, identityRecord = updatedIdentityRecord)
    }))
  }

   private def constructVendorBasedMatch(mergedMatch: Array[BestKYCMatch]): Map[DataSources.Value, Array[BestKYCMatch]] = {
    var mergedMatchMap = Map[DataSources.Value, Array[BestKYCMatch]]()

    mergedMatch.foreach { element =>
      val id = findDataSource(element.cluster.clusterId)
      id match {
        case Some(dataSources) => {
          if (!mergedMatchMap.contains(dataSources)) {
            mergedMatchMap = mergedMatchMap + (dataSources -> Array.empty[BestKYCMatch])  // Initialize with an empty array
          }
          mergedMatchMap = mergedMatchMap.updated(dataSources, Array.concat(mergedMatchMap(dataSources), Array(element)))
        }
        case _ => {
            // When Data Source is Null Do Nothing
        }
      }
    }

    mergedMatchMap
  }

  /**
   * Used by Prefill as of 13-Sep-2021
   * This filters best matches obtained from result matcher based on the criteria given in IDPLUS-5569
   *
   * @param resolvedReq
   * @param scoredMatches
   * @param trxId
   * @return
   */
  def getFilteredBestMatch(resolvedReq: KycEntitySearchRequest,
                           scoredMatches: Array[BestKYCMatch],
                           featureFlags: Map[FeatureFlags.FeatureFlag, Boolean])(implicit trxId: TrxId): Option[BestMatchEntity] = {
    val filteredBestMatches = try {
      resolvedReq.nationalId match {
        case Some(nationalId) => {
          val bestMatchArray = scoredMatches.filter {
            bst =>
              bst.piiMatchResults.hasValidPrefillSSN(bst.cluster.ssn, nationalId)(trxId)
          }
          trxLogger.debug(s"getFilteredBestMatch: best matches ${scoredMatches.length} and ssn filtered matches ${bestMatchArray.length} ")(trxId)
          Some(bestMatchArray)
        }
        case None => {
          trxLogger.debug("NationalId not avaialble in input")(trxId)
          None
        }
      }
    }
    catch {
      case NonFatal(ex) => {
        metrics.increment("bestmatch.filter.failure", "exception:" + ex)
        None
      }
    }
    populateBestMatchData(filteredBestMatches, resolvedReq, featureFlags = featureFlags)(trxId)
  }

  /**
   * Populates the best match data for the given scored-matches.
   *
   * @param scoredBestMatches
   * @param trxId
   * @return
   */
  def populateBestMatchData(scoredBestMatches: Option[Array[BestKYCMatch]], resolvedReq: KycEntitySearchRequest, isPrefill: Boolean = false, featureFlags: Map[FeatureFlags.FeatureFlag, Boolean])(implicit trxId: TrxId): Option[BestMatchEntity] = {
    try {
      val bestMatchEntity = scoredBestMatches match {
        case Some(x) => {
          val bestMatchData = loadData(x.headOption, resolvedReq, isPrefill, featureFlags = featureFlags)
          bestMatchData
        }
        case None => {
          metrics.increment("bestmatch.fetch.nodata")
          None
        }
      }
      bestMatchEntity
    } catch {
      case NonFatal(ex) => {
        metrics.increment("bestmatch.fetch.failure", "exception:" + ex)
        None
      }
      case _ => {
        metrics.increment("bestmatch.fetch.failure", "exception:" + "Unknown Exception")
        None
      }
    }
  }


  private def getDeceasedDate(bestKYCMatch: BestKYCMatch, indexOfClosestMatchSSN: Option[Int] = None): Option[String] = {
    indexOfClosestMatchSSN match {
      case Some(index) if index < bestKYCMatch.cluster.deceasedDate.length => Some(bestKYCMatch.cluster.deceasedDate(index)).flatMap(DateConversionFactory.formatDate)
      case Some(index) if index >= bestKYCMatch.cluster.deceasedDate.length => None
      case _ => bestKYCMatch.cluster.deceasedDate.headOption.flatMap(DateConversionFactory.formatDate)
    }
  }

  def getResolvedNineDigitNationalIDResults(searchRequest: KYCSearchRequestResolved,
                                            bestOfTwoSources: Option[BestKYCMatch],
                                            nationalIdResults: Elastic4sResult,
                                            equifaxQueryService: QueryService,
                                            enformionQueryService : QueryService,
                                            isEnformionEnabled: Boolean,
                                            mockDataID : Long
                                           ): Future[Elastic4sResult]= {

    if (searchRequest.nationalIdResolved.nonEmpty
      && bestOfTwoSources.nonEmpty
    ) {
      val inputSSN = searchRequest.nationalIdResolved.get.cleaned
      val resolvedSSN = getResolved9DigitSSN(inputSSN, bestOfTwoSources.get)
      if (resolvedSSN != inputSSN) {
        val nationalIdSearchQuery = ElasticSearchQueryFactory.generateNationalIdQuery(Some(resolvedSSN.value))
        if (nationalIdSearchQuery.nonEmpty) {
          metrics.timeFuture("additional.ssn.query.duration"){
            val equifaxQueryResults = searchNationalId(equifaxQueryService, nationalIdSearchQuery, "EQX", mockDataID, Set("resolvedQuery:true")).flatMap(_.withCleanedDataAsync(searchRequest.resolvedReq))
            val enformionQueryResults = if (isEnformionEnabled) searchNationalId(enformionQueryService, nationalIdSearchQuery, "ENF", mockDataID, Set("resolvedQuery:true")).flatMap(_.withCleanedDataAsync(searchRequest.resolvedReq)) else Future.successful(Elastic4sResult.empty)
            for {
              equifaxResults <- equifaxQueryResults
              enformionResults <- enformionQueryResults
            }yield {
              equifaxResults.appended(enformionResults)
            }}
        } else Future.successful(nationalIdResults)
      } else {
        Future.successful(nationalIdResults)
      }
    } else {
      Future.successful(nationalIdResults)
    }
  }

  def getAnchorDeceasedRecordUsingInputSSN(searchRequest: KYCSearchRequestResolved,
                                           bestOfTwoSources: Option[BestKYCMatch]
                                          ): Future[(Seq[AnchorDeceasedRecord],Option[AnchorDeceasedRecord])]  = {
    if (anchorEnabled) {
      val resolvedSsn = resolveTo9DigitsSsn(searchRequest, bestOfTwoSources)
      val inputSsn = searchRequest.nationalIdResolved.map(_.cleaned)
      resolvedSsn.orElse(inputSsn)
        .map(nationalId => AnchorLookupUtil.getDeceasedRecord(nationalId, anchorLookupDao, searchRequest.resolvedReq))
        .getOrElse(Future.successful((Seq.empty,None)))
    } else {
      Future.successful((Seq.empty,None))
    }
  }

  def resolveTo9DigitsSsn(searchRequest: KYCSearchRequestResolved, bestOfTwoSources: Option[BestKYCMatch]): Option[NationalId] = {
    if (searchRequest.nationalIdResolved.nonEmpty && bestOfTwoSources.nonEmpty) {
      val inputSSN = searchRequest.nationalIdResolved.get.cleaned
      val resolvedSSN = getResolved9DigitSSN(inputSSN, bestOfTwoSources.get)
      if (resolvedSSN != inputSSN) {
        Some(resolvedSSN)
      } else {
        None
      }
    } else {
      None
    }
  }

  def searchNationalId(queryService : QueryService, nationIDQuery : Option[String], vendor : String, mockDataID : Long, additionalTags : Set[String] = Set.empty) : Future[Elastic4sResult] = {
    nationIDQuery.map(nationalIdRequest => {
      val tags = MetricTags(
        serviceName = Some(serviceName),
        isInternal = Some(false),
        mSource = Some("client"),
        queryType = Some("kyc"),
        tags = Set("searchType:ssn") ++ additionalTags
      )
      val execRequest = queryService.execute(nationalIdRequest, mockDataID, vendor, "NATIONAL_IDENTITY_REQUEST")
        .withMetricTagsV2(
          metrics = prefixedMetrics,
          baseTags = tags
        )()

      execRequest.recoverWith {
        case e: Throwable =>
          logger.error("Unknown failure", e)
          metrics.increment("ssn.search.exception", "class:" + e.getClass.getSimpleName)
          Future.failed(e)
      }
    }).getOrElse(Future.successful(Elastic4sResult.empty))
  }

  def groupNamesByRowId(names: Option[Seq[PIIField]]): Map[String, Seq[String]] = {
    names.map(_.groupBy(_.rowId.getOrElse("")).mapValues(_.map(_.value)).toMap).getOrElse(Map.empty[String, Seq[String]])
  }

  def loadData(bestKYCMatch: Option[BestKYCMatch], resolvedReq: KycEntitySearchRequest, isPrefill: Boolean = false, is4DigitSsn: Boolean = false, featureFlags: Map[FeatureFlags.EnumVal, Boolean])(implicit trxId: TrxId): Option[BestMatchEntity] = {
    val customPreferences = resolvedReq.customPreferencesKyc
    val isSSNIssuedDateNewFormatEnabled = featureFlags.getOrElse(FeatureFlags.EnableSSNIssuedDateNewFormat, false)
    val isPrefillValidationEnabled = featureFlags.getOrElse(FeatureFlags.EnablePrefillBMEValidation, false)
    try {
      bestKYCMatch.headOption.flatMap {
        bestMatch => {
          val associatedAddresses = getAssociatedAddressList(bestMatch, customPreferences, resolvedReq, defaultMatchWeights)
          val associatedPhoneNumbers = getAssociatedPhoneNumberList(bestMatch, customPreferences, resolvedReq)
          val associatedEmails = getAssociatedEmailList(bestMatch, customPreferences, resolvedReq)
          val recentPhoneNumber: Option[String] = associatedPhoneNumbers.flatMap { phoneNumbers =>
            phoneNumbers.headOption.map(_.phoneNumber)
          }
          val prefillMatchedDOB = findClosestMatchedDOB(resolvedReq.dob.getOrElse(DOB("")).value, bestMatch.cluster.dob, resolvedReq)
          val nationalId = getClosestMatchedSSN(resolvedReq, bestMatch)
          val indexOfClosestMatchSSN: Option[Int] =
            if (bestMatch.cluster.ssn.isEmpty || StringUtils.isEmpty(nationalId) || bestMatch.cluster.ssn.indexOf(nationalId) == -1) None
            else Some(bestMatch.cluster.ssn.indexOf(nationalId))
          val deceasedDate = getDeceasedDate(bestMatch, indexOfClosestMatchSSN)
          val age = DOBResolverHelper.getResolvedAge(
            DOBResolverHelper.getFormattedDOBForPrefill(prefillMatchedDOB.dob).getOrElse(""),
            resolvedReq.submissionDate,
            deceasedDate.flatMap(DateConversionFactory.toDate)
          ).map(_.toInt)

          if (!isPrefill) {
            Some(BestMatchEntity(
              firstName = convertToProperCase(bestMatch.cluster.firstName.headOption),
              middleName = convertToProperCase(bestMatch.cluster.middleName.headOption),
              surName = convertToProperCase(bestMatch.cluster.surName.headOption),
              ssn = bestMatch.cluster.ssn.headOption,
              ssnIssued = DateConversionFactory.formatDate(bestMatch.cluster.ssnIssued.headOption.getOrElse("")),
              dob = DOBResolverHelper.getValidatedDOB(bestMatch.cluster.dob.headOption).map(date => formatDateToYYYYMMDD(date)),
              mobileNumber = recentPhoneNumber.orElse(bestMatch.cluster.mobileNumber.headOption),
              emailAddress = associatedEmails.flatMap(_.headOption.map(_.emailAddress)),
              streetAddress = convertToProperCase(bestMatch.cluster.streetAddress.headOption),
              city = convertToProperCase(bestMatch.cluster.city.headOption),
              zipCode = formatZipCode(bestMatch.cluster.zipCode.headOption),
              state = convertToUpperCase(bestMatch.cluster.state.headOption),
              ssnDeceased = bestMatch.cluster.ssnDeceased.headOption,
              cidDeceased = bestMatch.cluster.cidDeceased.headOption,
              clusterId = bestMatch.cluster.clusterId,
              associatedAddresses = associatedAddresses,
              associatedPhoneNumbers = associatedPhoneNumbers,
              associatedEmails = associatedEmails,
              deceasedDate = getDeceasedDate(bestMatch),
              age = age
            ))
          }
          else {
            val prefillMatchedName = findClosestMatchedNames(
              resolvedReq.firstName.value,
              resolvedReq.surName.value,
              bestMatch.cluster.firstName,
              bestMatch.cluster.surName,
              bestMatch.cluster.middleName,
              bestMatch.cluster.suffixName,
              bestMatch.identityRecord
            ).getOrElse(PrefillMatchedName())
            val cleanedNationId = if(nationalId.length > 6) cleanNationalId(NationalId(nationalId)).value else nationalId

            val ssnIssuedDate = if (isSSNIssuedDateNewFormatEnabled) {
              val ssnIssuedYearFromLookupFile = Try(SSNUtil.ssnLookup(Some(cleanedNationId), useV2 = true).map(lookup => {
                val lowYear = lookup.ssnYearLow
                val highYear = lookup.ssnYearHigh
                (lowYear, highYear) match {
                  case (low, high) if low.nonEmpty && high.nonEmpty => s"$low-$high"
                  case (low, _) if low.nonEmpty => s"$low-$low"
                  case (_, high) if high.nonEmpty => s"$high-$high"
                  case _ => ""
                }
              })).toOption.flatten

              ssnIssuedYearFromLookupFile.orElse {
                val ssnIssuedYear = indexOfClosestMatchSSN match {
                  case Some(index) if index < bestMatch.cluster.ssnIssued.length =>
                    val year = bestMatch.cluster.ssnIssued(index)
                    if (year.nonEmpty) Some(year.slice(0, 4)) else None
                  case Some(index) if index >= bestMatch.cluster.ssnIssued.length => None
                  case _ =>
                    val year = bestMatch.cluster.ssnIssued.headOption.getOrElse("")
                    if (year.nonEmpty) Some(year.slice(0, 4)) else None
                }
                Try {
                  val skipSSNIssuedDate = shouldSkipSSNIssuedDate(resolvedReq, ssnIssuedYear, bestMatch, extractFullYear = true)
                  if (skipSSNIssuedDate) None else ssnIssuedYear.map(year => s"$year-$year")
                }.toOption.flatten.orElse(ssnIssuedYear.map(year => s"$year-$year"))
              }
            } else {

              val ssnIssuedYearFromLookupFile = Try(SSNUtil.getSSNIssued(Some(cleanedNationId), useV2 = true).flatMap(DateConversionFactory.formatDate)).toOption.flatten

              ssnIssuedYearFromLookupFile.orElse {
                val ssnIssuedYear = indexOfClosestMatchSSN match {
                  case Some(index) if index < bestMatch.cluster.ssnIssued.length => DateConversionFactory.formatDate(bestMatch.cluster.ssnIssued(index))
                  case Some(index) if index >= bestMatch.cluster.ssnIssued.length => None
                  case _ => DateConversionFactory.formatDate(bestMatch.cluster.ssnIssued.headOption.getOrElse(""))
                }
                Try {
                  val skipSSNIssuedDate = shouldSkipSSNIssuedDate(resolvedReq, ssnIssuedYear, bestMatch, extractFullYear = false)
                  if (skipSSNIssuedDate) None else ssnIssuedYear
                }.toOption.flatten.orElse(ssnIssuedYear)
              }
            }

            val ssnDeceased = indexOfClosestMatchSSN match {
              case Some(index) if index < bestMatch.cluster.ssnDeceased.length => Some(bestMatch.cluster.ssnDeceased(index))
              case Some(index) if index >= bestMatch.cluster.ssnDeceased.length => None
              case _ => bestMatch.cluster.ssnDeceased.headOption
            }
            val cidDeceased = indexOfClosestMatchSSN match {
              case Some(index) if index < bestMatch.cluster.cidDeceased.length => Some(bestMatch.cluster.cidDeceased(index))
              case Some(index) if index >= bestMatch.cluster.cidDeceased.length => None
              case _ => bestMatch.cluster.cidDeceased.headOption
            }
            val closestMatchedFullName = Seq(
              prefillMatchedName.firstName,
              prefillMatchedName.middleName,
              prefillMatchedName.surName,
              prefillMatchedName.suffixName
            ).filter(_.nonEmpty).map(_.get).mkString(" ")
            val nameAliases = getNameAliases(
              closestMatchedFullName,
              firstNames = groupNamesByRowId(bestMatch.identityRecord.map(_.firstName)),
              middleNames = groupNamesByRowId(bestMatch.identityRecord.map(_.middleName)),
              surNames = groupNamesByRowId(bestMatch.identityRecord.map(_.surName)),
              suffixNames = groupNamesByRowId(bestMatch.identityRecord.map(_.suffixName))
            )
            /**
             * When making any modifications to PII, make sure the same has also been done in KYCPlusHelper.getUnsharedPIIMaskedBME
             * This is to ensure unnecessary masking doesn't happen.
             * zipCode issue occurred -> https://jira.socure.com/browse/IDPLUS-9846
             */
            Some(BestMatchEntity(
              firstName = convertToProperCase(prefillMatchedName.firstName),
              middleName = convertToProperCase(prefillMatchedName.middleName),
              surName = convertToProperCase(prefillMatchedName.surName),
              suffixName = prefillMatchedName.suffixName,
              ssn = Option(cleanedNationId),
              ssnIssued = ssnIssuedDate,
              dob = DOBResolverHelper.getFormattedDOBForPrefill(prefillMatchedDOB.dob),
              mobileNumber = recentPhoneNumber.orElse(bestMatch.bestMatchedElements.get("mobileNumber")),
              emailAddress = associatedEmails.flatMap(_.headOption.map(_.emailAddress)),
              streetAddress = convertToProperCase(bestMatch.bestMatchedElements.get("streetAddress")),
              city = convertToProperCase(bestMatch.bestMatchedElements.get("city")),
              zipCode = formatZipCode(bestMatch.bestMatchedElements.get("zipCode")),
              state = convertToUpperCase(bestMatch.bestMatchedElements.get("state")),
              ssnDeceased = ssnDeceased,
              cidDeceased = cidDeceased,
              clusterId = bestMatch.cluster.clusterId,
              associatedAddresses = associatedAddresses,
              associatedPhoneNumbers = associatedPhoneNumbers,
              associatedEmails = associatedEmails,
              deceasedDate = deceasedDate,
              matchMetadata = Some(MatchMetadata(
                firstName = prefillMatchedName.matchCriteriaFN,
                surName = prefillMatchedName.matchCriteriaSN,
                dob = Some(prefillMatchedDOB.matchCriteria)
              )),
              nameAliases = Some(nameAliases),
              age = age,
              isValidMatch = Option(MergedEntityUtil.validateInputVsBMEMatch(resolvedReq, bestMatch)),
              totalWeight = {
                val matchedPiis = bestMatch.piiMatchResults.filter(_._2 == 1).keys.toSet
                Option(defaultMatchWeights.getPiiVsWeight(is4DigitSsn).filterKeys(matchedPiis.contains).values.sum)
              },
              prefillValidationStatus = if (isPrefillValidationEnabled) {
                Some(BestMatchEntityPresenterUtils.validatePrefillStatus(request = resolvedReq, piiMatchResults = bestMatch.piiMatchResults))
              } else None
            ))
          }
        }
      }
    } catch {
      case NonFatal(ex) => {
        trxLogger.error(s"Error in loadData", ex)
        metrics.increment("prefill.load_data.failure", s"ex_class:${ex.getClass.getSimpleName}")
        None
      }
    }
  }

  private def getNameAliases(
                              closestMatchedName: String,
                              firstNames: Map[String, Seq[String]],
                              middleNames: Map[String, Seq[String]],
                              surNames: Map[String, Seq[String]],
                              suffixNames: Map[String, Seq[String]]
                            ): Seq[String] = {

    val allRowIds = firstNames.keys ++ middleNames.keys ++ surNames.keys ++ suffixNames.keys

    allRowIds.toSeq.map { rowId =>
        val firstNameOptions = firstNames.getOrElse(rowId, Seq(""))
        val surNameOptions = surNames.getOrElse(rowId, Seq(""))
        val middleNameOptions = middleNames.getOrElse(rowId, Seq(""))
        val suffixNameOptions = suffixNames.getOrElse(rowId, Seq(""))

        val isPrimary = firstNameOptions.exists(_.nonEmpty) || surNameOptions.exists(_.nonEmpty)

        val list = (for {
          firstName <- firstNameOptions.view
          surName <- surNameOptions.view
          middleName <- middleNameOptions.view
          suffixName <- suffixNameOptions.view
          fullName = Seq(firstName, middleName, surName, suffixName).filter(_.nonEmpty)
          if fullName.nonEmpty && !closestMatchedName.equalsIgnoreCase(fullName.mkString(" "))
        } yield fullName).take(10).toList.sortBy(list => list.length * -1)
        (isPrimary, list)
      }.sortWith {
        case (list1, list2) =>
          if (list1._1 && !list2._1) true
          else if (!list1._1 && list2._1) false
          else list1._2.length > list2._2.length
      }
      .flatMap(_._2)
      .distinct.take(4)
      .map(_.mkString(" "))
  }


  def populateTopNBestMatchData(scoredBestMatches: Option[Array[BestKYCMatch]], count: Integer = 20)(implicit trxId: TrxId): Option[List[BestMatchEntityWithAlias]] = {
    try {
      val bestMatchEntity = scoredBestMatches match {
        case Some(x) => {
          val bestMatchArray: List[BestMatchEntityWithAlias] = x.take(count).map {
            bestMatch =>
              BestMatchEntityWithAlias(
                firstName = Some(bestMatch.cluster.firstName),
                middleName = Some(bestMatch.cluster.middleName),
                surName = Some(bestMatch.cluster.surName),
                suffixName = Some(bestMatch.cluster.suffixName),
                ssn = Some(bestMatch.cluster.ssn),
                dob = Some(bestMatch.cluster.dob),
                mobileNumber = Some(bestMatch.cluster.mobileNumber),
                streetAddress = Some(bestMatch.cluster.streetAddress),
                associatedEmails = Some(bestMatch.cluster.emailAddress),
                city = Some(bestMatch.cluster.city),
                zipCode = Some(bestMatch.cluster.zipCode),
                state = Some(bestMatch.cluster.state),
                ssnDeceased = Some(bestMatch.cluster.ssnDeceased),
                cidDeceased = Some(bestMatch.cluster.cidDeceased),
                clusterId = bestMatch.cluster.clusterId)
          }.toList
          Some(bestMatchArray)
        }
        case None => {
          metrics.increment("bestmatch.topn.fetch.nodata")
          None
        }
      }
      bestMatchEntity
    } catch {
      case NonFatal(ex) => {
        metrics.increment("bestmatch.topn.fetch.failure", "exception:" + ex)
        None
      }
      case _ => {
        metrics.increment("bestmatch.topn.fetch.failure", "exception:" + "Unknown Exception")
        None
      }
    }
  }

  private def getTopNIdentityRecords(scoredBestMatches: Option[Array[BestKYCMatch]], count: Int = 20): Option[List[IdentityRecord]] = {
    try {
      scoredBestMatches.map { list =>
        list.take(count).map(_.identityRecord.getOrElse(IdentityRecord.empty)).toList
      }
    } catch {
      case _: Exception =>
        None
    }
  }

  private def getTopNIdentityRecordsByCluster(scoredBestMatches: Option[Array[BestKYCMatch]], count: Int = 10): Option[List[IdentityRecord]] = {
    val validPrefixes = Set("tid", "lid", "docv", "bld", "asl", "edv", "attm")

    scoredBestMatches.map { bestKYCMatch =>
      bestKYCMatch.flatMap(_.identityRecord)
        .flatMap { record =>
          record.clusterId.find(id => validPrefixes.exists(id.toLowerCase.startsWith)).map(prefix => (prefix, record))
        }
        .groupBy(_._1)
        .flatMap { case (_, records) =>
          records.map(_._2).take(count)
        }
        .toList
    }
  }


  private def getVendorsFromClusterID(clusterIDStr: Option[String]): List[String] = {
    val clusterIDs = clusterIDStr.map(_.split(",")).getOrElse(Array.empty)
    clusterIDs.map(clusterID => findVendorFromClusterID(Some(clusterID))).distinct.toList
  }

  def logAnchorMetrics(mergedNationalIdResults: Elastic4sResult, anchorRecordUsingInputSSN: Option[AnchorDeceasedRecord], searchRequest: KYCSearchRequestResolved, accountId: Long) : Unit = {
    try {
      val inputSSN = searchRequest.resolvedReq.nationalId.getOrElse(NationalId(""))
      val nationalIDResolved = searchRequest.nationalIdResolved
      val nationIdResults = mergedNationalIdResults.attributes
      if(nationIdResults.nonEmpty &&  inputSSN.value.nonEmpty && nationalIDResolved.nonEmpty) {
        val originalId = nationalIDResolved.get.original
        val ssnDeceased = nationIdResults.filter(cluster => cluster.ssn.zip(cluster.ssnDeceased).exists{case (ssn:String,ssnDeceased:String) => matchNationalId(inputSSN, NationalId(ssn), originalId = originalId) && ssnDeceased.contains("1")})
        val anchorDeceased = anchorRecordUsingInputSSN.nonEmpty

        if(ssnDeceased.isEmpty && anchorDeceased) {
          metrics.increment("deceased.ssn.found.only.in.anchor", s"account_id:$accountId")
        }

        if(ssnDeceased.nonEmpty && anchorDeceased) {
          metrics.increment("deceased.ssn.found.in.both", s"account_id:$accountId")
        }

        if(!anchorDeceased && ssnDeceased.nonEmpty) {
          metrics.increment("deceased.ssn.found.only.in.existing.record", s"account_id:$accountId")
        }
      }
    } catch {
      case ex: Exception => logger.warn(s"error in logging anchor metrics $ex")
    }
  }

  private def convertToProperCase(value: Option[String]): Option[String] = value.map(convertToProperCase)
  private def convertToUpperCase(value: Option[String]): Option[String] = value.map(convertToUpperCase)

  private def convertToProperCase(value: String): String = {
    if (value.nonEmpty) {
      value.toLowerCase.split(' ').map(_.capitalize).mkString(" ")
    } else value
  }

  private def convertToUpperCase(value: String): String = {
    if (value.nonEmpty) {
      value.toUpperCase
    } else value
  }

  private def formatZipCode(value: Option[String]): Option[String] = value.map(formatZipCode)
  private def formatZipCode(value: String): String = AddressResolverHelper.formatZipCode(value)

  private def isValidEmail(email: String): Boolean = {
    val emailValidator = EmailValidator.getInstance()
    emailValidator.isValid(email)
  }

  private def shouldSkipSSNIssuedDate(resolvedReq: KycEntitySearchRequest,
                                    ssnIssuedYear: Option[String],
                                    bestMatch: BestKYCMatch,
                                    extractFullYear: Boolean = true): Boolean = {
    if (resolvedReq.dob.nonEmpty) {
      val suppressSSNIssuedPriorToDOB = DOBResolverHelper.shouldSuppressSSNIssuedPriorToDOBFiring(bestMatch.piiMatchResults)
      val yearOfBirth = resolvedReq.dob.get.value.slice(0, 4).toInt
      // In this case, EXVAL.100025(EX_SSN_PRIOR_TO_DOB) wouldn't have fired even though ssn issued prior to dob, so to avoid conflict, skipped ssn issued year in bme
      suppressSSNIssuedPriorToDOB && ssnIssuedYear.exists { year =>
        if (extractFullYear) year.toInt < yearOfBirth - 1
        else year.slice(0, 4).toInt < yearOfBirth - 1
      }
    } else false
  }
}
