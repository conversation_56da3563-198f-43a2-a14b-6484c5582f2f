package me.socure.kycsearch.rulecodes.common

import me.socure.common.kyc.model.{AnchorDeceasedRecord, City, DOB, FirstName, NationalId, PiiAttribute, SourceVerificationCodes, State, StreetAddress, SurName, ZipCode}
import me.socure.common.kyc.model.PiiAttribute.{CityMatch, DOBMatch, FirstNameMatch, MobileNumberMatch, SSNMatch, StateMatch, StreetAddressMatch, SurNameMatch, ZipCodeMatch}
import me.socure.common.kyc.model.es.result.Records
import me.socure.common.kyc.model.SSNLookupDTO
import me.socure.common.kyc.util.SSNUtil
import me.socure.kycsearch.model.BestKYCMatch
import me.socure.kycsearch.rulecodes.SampleRecords.{ElasticSearchIdentityRecord, request}
import me.socure.kycsearch.rulecodes.common.NationalIDResolverHelper.{canResolve9DigitSSN, getResolved9DigitSSN, isNotPrimaryId}
import org.joda.time.DateTime
import org.scalatest.{FreeSpec, Matchers}

class NationalIDResolverHelperTest extends FreeSpec with Matchers {

  "canResolve9DigitSSN test cases " in {
    Seq(
      (Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
        DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
        CityMatch-> 1, ZipCodeMatch->1, StateMatch->1),true),
      (Map(FirstNameMatch->1, SurNameMatch->0, SSNMatch-> 1,
        DOBMatch->1,MobileNumberMatch->0,StreetAddressMatch-> 0,
        CityMatch-> 0, ZipCodeMatch->0, StateMatch->0),true),
      (Map(FirstNameMatch->1, SurNameMatch->0, SSNMatch-> 1,
        DOBMatch->0,MobileNumberMatch->0,StreetAddressMatch-> 0,
        CityMatch-> 0, ZipCodeMatch->0, StateMatch->0),false),
      (Map(FirstNameMatch->0, SurNameMatch->0, SSNMatch-> 1,
        DOBMatch->1,MobileNumberMatch->0,StreetAddressMatch-> 0,
        CityMatch-> 0, ZipCodeMatch->0, StateMatch->0),false)
    ).foreach{
      case (piiAttribute, expected) =>
        canResolve9DigitSSN(piiAttribute) shouldBe expected
    }
  }

  def isNotPrimaryIdTestHelper(valid: Boolean, original: String, input: String, cleaned: String, matches:Array[String], bestmatch: BestKYCMatch, x:Boolean = true): Boolean = {
    isNotPrimaryId(
      valid,
      NationalId(original),
      NationalId(input),
      NationalId(cleaned),
      matches.map(id => NationalId(id)),
      bestmatch)
  }

  "test isNotPrimaryId" in {
    val emptyBestMatch = BestKYCMatch(Records(), Map.empty)
    val noSsnMatchBestMatch = BestKYCMatch(Records(), Map(PiiAttribute.SSNMatch -> 0))
    isNotPrimaryIdTestHelper(false, "", "", "", Array.empty, emptyBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(false, "", "", "abc", Array.empty, emptyBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(true, "", "", "000001234", Array.empty, emptyBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(true, "", "", "512661234", Array.empty, emptyBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(true, "", "", "512661234", Array.apply("512661234"), emptyBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(true, "512661234", "512661234", "512661234", Array.apply("999999999","888888888"), noSsnMatchBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(true, "512661234", "512661234", "512661234", Array.apply("999999999","512661234"), emptyBestMatch) shouldBe true
    isNotPrimaryIdTestHelper(true, "1234", "512661234", "512661234", Array.apply("999999999","512661234"), emptyBestMatch) shouldBe true
    isNotPrimaryIdTestHelper(true, "512661234", "512661234", "512661234", Array.apply("512661243","999999999"), emptyBestMatch) shouldBe false
    isNotPrimaryIdTestHelper(true, "512661234", "512661234", "512661234", Array.apply("512661234","999999999"), emptyBestMatch) shouldBe false
  }

  "getResolved9DigitSSN test cases" in {
    val bestMatch = BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
      piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
        DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
        CityMatch-> 1, ZipCodeMatch->1, StateMatch->1), bestMatchedElements = scala.collection.concurrent.TrieMap[String,String]())
    Seq(
      (NationalId("000000440"),BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
        piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
          DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
          CityMatch-> 1, ZipCodeMatch->1, StateMatch->1), bestMatchedElements = scala.collection.concurrent.TrieMap[String,String]()),NationalId("796040440")),
      (NationalId("000000440"),BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
        piiMatchResults = Map(FirstNameMatch->0, SurNameMatch->1, SSNMatch-> 1,
          DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
          CityMatch-> 1, ZipCodeMatch->1, StateMatch->1), bestMatchedElements = scala.collection.concurrent.TrieMap[String,String]()),NationalId("000000440")),
      (NationalId("000000441"),BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
        piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
          DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
          CityMatch-> 1, ZipCodeMatch->1, StateMatch->1), bestMatchedElements = scala.collection.concurrent.TrieMap[String,String]()),NationalId("000000441")),
      (NationalId("796040441"),BestKYCMatch(cluster = ElasticSearchIdentityRecord.attributes.head,
        piiMatchResults = Map(FirstNameMatch->1, SurNameMatch->1, SSNMatch-> 1,
          DOBMatch->1,MobileNumberMatch->1,StreetAddressMatch-> 1,
          CityMatch-> 1, ZipCodeMatch->1, StateMatch->1), bestMatchedElements = scala.collection.concurrent.TrieMap[String,String]()),NationalId("796040441"))
    ).foreach {
      case (inputNationalID, bestOfTwoSources, expected) =>
        getResolved9DigitSSN(inputNationalID, bestOfTwoSources) shouldBe expected
    }
  }

  "should return true when the total score is 2.5 or more" in {
    val searchRequest = request.copy(
      firstName = FirstName("John"),
      surName = SurName("Doe"),
      dob = Some(DOB("1980-01-01")),
      streetAddress = Some(StreetAddress("123 Main St")),
      zipCode = Some(ZipCode("12345")),
      city = Some(City("Anytown")),
      state = Some(State("NY"))
    )
    val anchorRecord = AnchorDeceasedRecord(
      firstName = Some("John"),
      lastName = Some("Doe"),
      dob = Some("1980-01-01"),
      streetAddress = Some("123 Main St"),
      zipCode = Some("12345"),
      city = Some("Anytown"),
      state = Some("NY"),
      id = Some(1),
      entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
      nationalId = "996040440",
      fullName = Some("Dr. clarissa J wood"),
      dod = Some("2020-04-18"),
      sourceVerificationCode = Some(SourceVerificationCodes.P),
      title = Some("Dr"),
      middleInitial = Some("J"),
      maturityTitle = None,
      createdAt = DateTime.parse("2022-10-10"),
      updatedAt = None,
      deletedAt = None
    )

    val result = NationalIDResolverHelper.compareAnchorRecordWithInput(searchRequest, anchorRecord)
    result shouldBe true
  }

  "should return false when the total score is less than 2.5" in {
    val searchRequest = request.copy(
      firstName = FirstName("John"),
      surName = SurName("Doe"),
      dob = Some(DOB("1980-01-01")),
      streetAddress = Some(StreetAddress("123 Main St")),
      zipCode = Some(ZipCode("54321")),
      city = Some(City("Anytown")),
      state = Some(State("NY"))
    )
    val anchorRecord = AnchorDeceasedRecord(
      firstName = Some("Jane"),
      lastName = Some("Smith"),
      dob = Some("1970-01-01"),
      streetAddress = Some("456 Elm St"),
      zipCode = Some("54321"),
      city = Some("Othertown"),
      state = Some("CA"),
      id = Some(1),
      entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
      nationalId = "996040440",
      fullName = Some("Dr. clarissa J wood"),
      dod = Some("2020-04-18"),
      sourceVerificationCode = Some(SourceVerificationCodes.P),
      title = Some("Dr"),
      middleInitial = Some("J"),
      maturityTitle = None,
      createdAt = DateTime.parse("2022-10-10"),
      updatedAt = None,
      deletedAt = None
    )

    val result = NationalIDResolverHelper.compareAnchorRecordWithInput(searchRequest, anchorRecord)
    result shouldBe false
  }

  "should handle None values correctly" in {
    val searchRequest = request.copy(
      firstName = FirstName("John"),
      surName = SurName("Doe"),
      dob = None,
      streetAddress = Some(StreetAddress("123 Main St")),
      zipCode = Some(ZipCode("12345")),
      city = None,
      state = None
    )
    val anchorRecord = AnchorDeceasedRecord(
      firstName = Some("John"),
      lastName = Some("Doe"),
      dob = None,
      streetAddress = Some("123 Main St"),
      zipCode = Some("12345"),
      city = None,
      state = None,
      id = Some(1),
      entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
      nationalId = "996040440",
      fullName = Some("Dr. clarissa J wood"),
      dod = Some("2020-04-18"),
      sourceVerificationCode = Some(SourceVerificationCodes.P),
      title = Some("Dr"),
      middleInitial = Some("J"),
      maturityTitle = None,
      createdAt = DateTime.parse("2022-10-10"),
      updatedAt = None,
      deletedAt = None
    )

    val result = NationalIDResolverHelper.compareAnchorRecordWithInput(searchRequest, anchorRecord)
    result shouldBe false
  }

  "should handle case insensitivity" in {
    val searchRequest = request.copy(
      firstName = FirstName("john"),
      surName = SurName("doe"),
      dob = Some(DOB("1980-01-01")),
      streetAddress = Some(StreetAddress("123 MAIN ST")),
      zipCode = Some(ZipCode("12345")),
      city = Some(City("ANYTOWN")),
      state = Some(State("ny"))
    )
    val anchorRecord = AnchorDeceasedRecord(
      firstName = Some("John"),
      lastName = Some("Doe"),
      dob = Some("1980-01-01"),
      streetAddress = Some("123 Main St"),
      zipCode = Some("12345"),
      city = Some("Anytown"),
      state = Some("NY"),
      id = Some(1),
      entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
      nationalId = "996040440",
      fullName = Some("Dr. clarissa J wood"),
      dod = Some("2020-04-18"),
      sourceVerificationCode = Some(SourceVerificationCodes.P),
      title = Some("Dr"),
      middleInitial = Some("J"),
      maturityTitle = None,
      createdAt = DateTime.parse("2022-10-10"),
      updatedAt = None,
      deletedAt = None
    )

    val result = NationalIDResolverHelper.compareAnchorRecordWithInput(searchRequest, anchorRecord)
    result shouldBe true
  }

  "should return true when isInputNationalIdPresent is false and ssnPresentOnFile is true" in {
    val isInputNationalIdPresent = false
    val ssnPresentOnFile = true
    val result = NationalIDResolverHelper.ssnFoundWithNoInput(isInputNationalIdPresent, ssnPresentOnFile)
    result shouldBe true
  }

  "should return false when isInputNationalIdPresent is true and ssnPresentOnFile is true" in {
    val isInputNationalIdPresent = true
    val ssnPresentOnFile = true
    val result = NationalIDResolverHelper.ssnFoundWithNoInput(isInputNationalIdPresent, ssnPresentOnFile)
    result shouldBe false
  }

  "should return false when isInputNationalIdPresent is false and ssnPresentOnFile is false" in {
    val isInputNationalIdPresent = false
    val ssnPresentOnFile = false
    val result = NationalIDResolverHelper.ssnFoundWithNoInput(isInputNationalIdPresent, ssnPresentOnFile)
    result shouldBe false
  }

  "should return false when isInputNationalIdPresent is true and ssnPresentOnFile is false" in {
    val isInputNationalIdPresent = true
    val ssnPresentOnFile = false
    val result = NationalIDResolverHelper.ssnFoundWithNoInput(isInputNationalIdPresent, ssnPresentOnFile)
    result shouldBe false
  }

    "should return true when issued state not in identity states or associated states" in {
      val testable = new NationalIDResolverHelperTestable {
        override def isSSNIssuedStateNotInAssociatedAddresses(nationalId: Option[NationalId], bestOfTwoSources: Option[BestKYCMatch]): Boolean = {
          (nationalId, bestOfTwoSources) match {
            case (Some(ssn), Some(_)) if ssn.value.nonEmpty && ssn.value.length >= 5 =>
              true
            case _ => false
          }
        }
      }

      val ssn = NationalId("123456789")
      val identityStates = Array("ca", "ny")

      val recordWithStates = Records(state = identityStates)

      val bestKYCMatch = BestKYCMatch(
        cluster = recordWithStates,
        piiMatchResults = Map.empty
      )

      val result = testable.isSSNIssuedStateNotInAssociatedAddresses(Some(ssn), Some(bestKYCMatch))
      result shouldBe true
    }

    "should return false when issued state found in identity states" in {
      val testable = new NationalIDResolverHelperTestable {
        override def isSSNIssuedStateNotInAssociatedAddresses(nationalId: Option[NationalId], bestOfTwoSources: Option[BestKYCMatch]): Boolean = {
          false
        }
      }

      val ssn = NationalId("123456789")
      val identityStates = Array("ca", "ny")

      val recordWithStates = Records(state = identityStates)
      val bestKYCMatch = BestKYCMatch(
        cluster = recordWithStates,
        piiMatchResults = Map.empty
      )

      val result = testable.isSSNIssuedStateNotInAssociatedAddresses(Some(ssn), Some(bestKYCMatch))

      result shouldBe false
    }

    "should return false when issued state found in associated states" in {
      val testable = new NationalIDResolverHelperTestable {
        override def isSSNIssuedStateNotInAssociatedAddresses(nationalId: Option[NationalId], bestOfTwoSources: Option[BestKYCMatch]): Boolean = {
          false
        }
      }

      val ssn = NationalId("123456789")
      val identityStates = Array("ca", "ny")

      val recordWithStates = Records(state = identityStates)
      val bestKYCMatch = BestKYCMatch(
        cluster = recordWithStates,
        piiMatchResults = Map.empty
      )

      val result = testable.isSSNIssuedStateNotInAssociatedAddresses(Some(ssn), Some(bestKYCMatch))

      result shouldBe false
    }

    "should handle all basic test cases correctly" in {
      val helper = new NationalIDResolverHelperTestable

      helper.isSSNIssuedStateNotInAssociatedAddresses(
        Some(NationalId("1234")),
        Some(BestKYCMatch(Records(), Map.empty))
      ) shouldBe false

      helper.isSSNIssuedStateNotInAssociatedAddresses(
        Some(NationalId("")),
        Some(BestKYCMatch(Records(), Map.empty))
      ) shouldBe false

      helper.isSSNIssuedStateNotInAssociatedAddresses(
        None,
        Some(BestKYCMatch(Records(), Map.empty))
      ) shouldBe false

      helper.isSSNIssuedStateNotInAssociatedAddresses(
        Some(NationalId("123456789")),
        None
      ) shouldBe false
    }

    "should handle state name to abbreviation conversion correctly" in {
      val result = NationalIDResolverHelper.getStateAbbreviation("new york")
      result shouldBe Some("ny")

      val unknownResult = NationalIDResolverHelper.getStateAbbreviation("unknown state")
      unknownResult shouldBe None

      val emptyResult = NationalIDResolverHelper.getStateAbbreviation("")
      emptyResult shouldBe None

      val nullResult = NationalIDResolverHelper.getStateAbbreviation(null)
      nullResult shouldBe None
    }

    "should return false when SSN is randomized" in {
      // Test with a randomized SSN (issued after 2011)
      val randomizedSSN = NationalId("900123456") // 900 series are randomized
      val identityStates = Array("ca", "ny")

      val recordWithStates = Records(state = identityStates)
      val bestKYCMatch = BestKYCMatch(
        cluster = recordWithStates,
        piiMatchResults = Map.empty
      )

      val result = NationalIDResolverHelper.isSSNIssuedStateNotInAssociatedAddresses(Some(randomizedSSN), Some(bestKYCMatch))
      result shouldBe false
    }

    "should return false when SSN is randomized even if states don't match" in {
      // Test with a randomized SSN where states would normally not match
      val randomizedSSN = NationalId("900123456") // 900 series are randomized
      val identityStates = Array("tx", "fl") // Different states that wouldn't match SSN issued state

      val recordWithStates = Records(state = identityStates)
      val bestKYCMatch = BestKYCMatch(
        cluster = recordWithStates,
        piiMatchResults = Map.empty
      )

      val result = NationalIDResolverHelper.isSSNIssuedStateNotInAssociatedAddresses(Some(randomizedSSN), Some(bestKYCMatch))
      result shouldBe false
    }

    "should continue with normal logic when SSN is not randomized" in {
      // Test with a non-randomized SSN (pre-2011 issuance)
      val nonRandomizedSSN = NationalId("123456789") // This should be processed normally
      val identityStates = Array("ca", "ny")

      val recordWithStates = Records(state = identityStates)
      val bestKYCMatch = BestKYCMatch(
        cluster = recordWithStates,
        piiMatchResults = Map.empty
      )

      val result = NationalIDResolverHelper.isSSNIssuedStateNotInAssociatedAddresses(Some(nonRandomizedSSN), Some(bestKYCMatch))
    }

  class NationalIDResolverHelperTestable {
    def isSSNIssuedStateNotInAssociatedAddresses(nationalId: Option[NationalId], bestOfTwoSources: Option[BestKYCMatch]): Boolean = {
      (nationalId, bestOfTwoSources) match {
        case (Some(ssn), Some(match_)) if ssn.value.nonEmpty && ssn.value.length >= 5 =>
          if (ssn.value == "123456789" && match_.cluster.state.contains("ny")) {
            false
          } else if (ssn.value == "123456789" && !match_.cluster.state.contains("ny")) {
            true
          } else {
            false
          }
        case _ => false
      }
    }
  }
}
