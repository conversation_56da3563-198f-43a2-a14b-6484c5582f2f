package me.socure.kycsearch.rulecodes.entity

import java.time.LocalDate
import java.time.temporal.ChronoUnit
import com.socure.domain.socure.scoring.RuleCodes
import com.socure.domain.socure.scoring.RuleCodes.{EXX_BEST_MATCH_SSN_IS_ITIN, EXX_BETTER_MATCHING_SSN_FOUND, EXX_LAST_FOUR_BEST_MATCH_SSN_IS_ITIN_NEW, EXX_NO_INPUT_BUT_SSN_FOUND}
import me.socure.kycsearch._
import me.socure.common.kyc.model._
import me.socure.common.kyc.model.es.result.{Elastic4sResult, Records}
import me.socure.common.kyc.util.CommonUtil.getPiiMatchResults
import me.socure.kycsearch.model.BestKYCMatch
import me.socure.kycsearch.rulecodes.SampleRecords.{ElasticSearchIdentityRecord, EmptyElasticSearchIdentityRecord, request, resultMatcher, sampleDto}
import me.socure.kycsearch.rulecodes.common.{<PERSON><PERSON><PERSON><PERSON><PERSON>, NationalIDResolverHelper}
import me.socure.kycsearch.rulecodes.entity.EntityNationalIDRuleCodeResolver.generateMapping
import org.apache.commons.lang3.SerializationUtils
import org.joda.time.DateTime
import org.scalatest.{FreeSpec, Matchers}

class EntityNationalIDRuleCodeResolverTest extends FreeSpec with Matchers {

  "Test isDeceasedIdentityAssociatedWithInputIdentity" in {
    import NationalIDResolverHelper.isDeceasedIdentityAssociatedWithInputIdentity
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(false, true, true, true, true, true, true, true, true), false) shouldBe false
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, true, true, true, true, true, true, true, true), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(false, false, false, true, false, false, false, false, false), true) shouldBe true

    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, true, false, false, true, true, true, true, true), false) shouldBe false

    // DOB Match
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, true, false, false, false, false, false, false), false) shouldBe false
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, true, false, true, false, false, false, false), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, true, false, false, true, false, false, true), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, true, false, false, true, true, true, false), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, true, false, false, true, true, false, false), false) shouldBe false
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(fn = false, ln = false, dob = true, ssn = false, mobile = true, street = false, city = false, state = false, zip = false), true) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(fn = false, ln = false, dob = true, ssn = false, mobile = true, street = false, city = false, state = false, zip = false), true) shouldBe true

    // SSN Match
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, false, true, false, false, false, false, false), false) shouldBe false
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, true, true, false, false, false, false, false), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, false, true, true, false, false, false, false), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, true, false, true, false, false, false, false, false), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, false, true, false, true, false, false, true), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, false, true, false, true, true, true, false), false) shouldBe true
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, false, true, false, false, false, true, true), false) shouldBe false
    isDeceasedIdentityAssociatedWithInputIdentity(getPiiMatchResults(true, false, false, true, false, false, true, true, false), false) shouldBe false
  }

  "SSN random: 796040440 is random" in {
    val ssnTest = "796040440"
    NationalIDResolverHelper.isRandomlyIssuedNationalId(NationalId(ssnTest), NationalId(ssnTest), NationalId(ssnTest), Elastic4sResult.empty) shouldBe true
  }
  "SSN random: 001130440 is random" in {
    val ssnTest = "001130440"
    NationalIDResolverHelper.isRandomlyIssuedNationalId(NationalId(ssnTest), NationalId(ssnTest), NationalId(ssnTest), Elastic4sResult.empty) shouldBe true
  }
  "SSN not random: 001120440 is not random" in {
    val ssnTest = "001120440"
    NationalIDResolverHelper.isRandomlyIssuedNationalId(NationalId(ssnTest), NationalId(ssnTest), NationalId(ssnTest), Elastic4sResult.empty) shouldBe false
  }
  "SSN random: 123456789 is random" in {
    val ssnTest = "123456789"
    NationalIDResolverHelper.isRandomlyIssuedNationalId(NationalId(ssnTest), NationalId(ssnTest), NationalId(ssnTest), Elastic4sResult.empty) shouldBe true
  }
  "SSN not random: 123466789 is not random" in {
    val ssnTest = "123466789"
    NationalIDResolverHelper.isRandomlyIssuedNationalId(NationalId(ssnTest), NationalId(ssnTest), NationalId(ssnTest), Elastic4sResult.empty) shouldBe false
  }

  "EntityNationalIDRuleCodeResolver" - {

    "should match low risk profile" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not match ssn and resolution fail with random ssn" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("846295711")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not match fuzzy ssn with fuzzy ssn enabled when ssn is completed wrong" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("846295711")),
        preferencesEntity = KYCPreferences(
          exactDob = true,
          exactSSN = false,
          dobMatchLogic = Some("exact_yyyy")
        ))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should match fuzzy ssn with fuzzy ssn enabled" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("796040441")),
        preferencesEntity = KYCPreferences(
          exactDob = true,
          exactSSN = false,
          dobMatchLogic = Some("exact_yyyy"),
          nationalIdMatchLogic = Some("partial")
        ))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = true,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not match fuzzy ssn with fuzzy ssn disabled" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("796040441")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should resolution should be exact - should not match ssn" in {
      val currentNationalId = request.nationalId.map(_.value).getOrElse("")
      val requestBadSSN = request.copy(nationalId = Some(NationalId(currentNationalId.dropRight(1) + "9")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "missing SSN should trigger missing ssn only" in {
      val requestBadSSN = request.copy(nationalId = None)
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = true,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 0,
        ssnPresentOnFile = false,
        nonITINSSNPresentOnFile = false,
        ssnFoundWithNoInputSSN = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }


    "should indicate SSN is invalid" in {
      val requestITIN = request.copy(nationalId = Some(NationalId("912345678")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = true,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = true,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestITIN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestITIN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, EmptyElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should indicate SSN is ITIN not found in public records" in {
      val requestITIN = request.copy(nationalId = Some(NationalId("912705678")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = true,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = true,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = true,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestITIN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestITIN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, EmptyElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes.get(RuleCodes.EXX_SSN_NOT_IN_PUBLIC_REC) shouldBe Some(1.0)
      rulecodes shouldBe expected
    }

    "should indicate SSN not found in public records" in {
      val requestSSN = request.copy(nationalId = Some(NationalId("846295711")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = true,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestSSN.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, EmptyElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes.get(RuleCodes.EXX_SSN_NOT_IN_PUBLIC_REC) shouldBe Some(1.0)
      rulecodes shouldBe expected
    }

    "should indicate SSN is ITIN and found in public records" in {
      val requestITIN = request.copy(nationalId = Some(NationalId("912705678")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = true,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = false
      )

      val recordsWithITIN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array("912705678")))
      val searchRecord = ElasticSearchIdentityRecord.copy(
        attributes = recordsWithITIN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestITIN.resolve(), searchRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestITIN.resolve(), matched.right.get.head, searchRecord, searchRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes.get(RuleCodes.EXX_SSN_NOT_IN_PUBLIC_REC) shouldBe Some(0.0)
      rulecodes shouldBe expected
    }

    "should indicate SSN found in public records" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), ElasticSearchIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes.get(RuleCodes.EXX_SSN_NOT_IN_PUBLIC_REC) shouldBe Some(0.0)
      rulecodes shouldBe expected
    }

    "should indicate deceased" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = true,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val deceasedAttr = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(cidDeceased = Array("1")))
      val deceasedIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = deceasedAttr
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), deceasedIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, deceasedIdentityRecord, deceasedIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should indicate deceased if ssndeceased = 1" in {
      val expected = generateMapping(
        isIdWithDeceased = true,
        isIdentityDeceased = true,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val deceasedAttr = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssnDeceased =Array("1")))
      val deceasedIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = deceasedAttr
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), deceasedIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, deceasedIdentityRecord, deceasedIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should handle first name match ssn" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val matchedSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array("796040440")))
      val matchedSSNIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = matchedSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), matchedSSNIdentityRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, matchedSSNIdentityRecord, matchedSSNIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "should handle first name match ssn with fuzzy check" in {

      val requestNickName = request.copy(firstName = FirstName("ash"))

      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matchedSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
      val matchedSSNIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = matchedSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestNickName.resolve(), matchedSSNIdentityRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(requestNickName.resolve(), matched.right.get.head, matchedSSNIdentityRecord, matchedSSNIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "should handle first name no match ssn" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val unMatchedSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array("846295711")))
      val unMatchedSSNIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = unMatchedSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), unMatchedSSNIdentityRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, unMatchedSSNIdentityRecord, unMatchedSSNIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    //Best match tests
    "First name and SSN matches with best matched record" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), ElasticSearchIdentityRecord, entity = false)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "First name matches and SSN mismatch with best matched record" in {
      val ssnMismatchRequest = request.copy(nationalId = Some(NationalId("796041234")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = ssnMismatchRequest.resolve(), ElasticSearchIdentityRecord, entity = false)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(ssnMismatchRequest.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "First name mismatch and SSN matches with best matched record" in {
      val firstNameMismatchRequest = request.copy(firstName = FirstName("Andrea"))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = firstNameMismatchRequest.resolve(), ElasticSearchIdentityRecord, entity = false)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(firstNameMismatchRequest.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "First name and SSN mismatch with best matched record" in {
      val firstNameAndSSNMismatchRequest = request.copy(firstName = FirstName("Andrea")).copy(nationalId = Some(NationalId("796041234")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = false,
        notInPubRecords = true,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = firstNameAndSSNMismatchRequest.resolve(), ElasticSearchIdentityRecord, entity = false)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(firstNameAndSSNMismatchRequest.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "should not indicate ssnDeceased even if cidDeceased is 1" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = true,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val deceasedAttr = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(cidDeceased =Array("1")))
      val deceasedIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = deceasedAttr
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), deceasedIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, deceasedIdentityRecord, deceasedIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should indicate SSN is not primary" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = true,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "846295711",
          "796040440"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should indicate SSN is primary" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "796040440",
          "846295711"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should indicate multiple application national identifiers" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = true,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "796040440",
          "846295711"
        ),ciRowId = Array(
          "860:EXX_CHM_44872046621799776507-944",
          "894060:EXX_CHM_54872046621799776507-945"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "Multiple SSNs in identity: should not fire because one SSN is coming from LFM" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "796040440",
          "846295711"
        ),ciRowId = Array(
          "860:EXX_CHM_44872046621799776507-944",
          "960:EXX_LFM_54872046621799776507-945"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "Multiple SSNs in identity: should not fire because SSNs close" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )


      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "796040440",
          "796040441"
        ),ciRowId = Array(
          "860:EXX_CHM_44872046621799776507-944",
          "960:EXX_CHM_54872046621799776507-945"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not last four iidn be ZERO" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("946645711")),
        preferencesKyc = KYCPreferences(
          exactDob = true,
          exactSSN = false,
          dobMatchLogic = None,
          nationalIdMatchLogic = Some("partial")
        ))

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      val bestMatchResult = BestMatchEntity(ssn = Some("934645711"))
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, bestKYCMatch = Some(bestMatchResult), dto = sampleDto)

      rulecodes.get(EXX_LAST_FOUR_BEST_MATCH_SSN_IS_ITIN_NEW) shouldBe Some(0.0)
    }

    "should not match iidn based on best match" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("000005711")),
        preferencesKyc = KYCPreferences(
          exactDob = true,
          exactSSN = false,
          dobMatchLogic = None,
          nationalIdMatchLogic = Some("partial")
        ))

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      val bestMatchResult = BestMatchEntity(ssn = Some("934645711"))
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, bestKYCMatch = Some(bestMatchResult), dto = sampleDto)

      rulecodes.get(EXX_LAST_FOUR_BEST_MATCH_SSN_IS_ITIN_NEW) shouldBe Some(1.0)
    }


    "should bme ssn iitn be one" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("946645711")),
        preferencesKyc = KYCPreferences(
          exactDob = true,
          exactSSN = false,
          dobMatchLogic = None,
          nationalIdMatchLogic = Some("partial")
        ))

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      val bestMatchResult = BestMatchEntity(ssn = Some("934645711"))
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, bestKYCMatch = Some(bestMatchResult), dto = sampleDto)

      rulecodes.get(EXX_BEST_MATCH_SSN_IS_ITIN) shouldBe Some(1.0)
    }

    "should bme ssn be one even if input does not match" in {
      val requestBadSSN = request.copy(nationalId = Some(NationalId("000005711")),
        preferencesKyc = KYCPreferences(
          exactDob = true,
          exactSSN = false,
          dobMatchLogic = None,
          nationalIdMatchLogic = Some("partial")
        ))

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestBadSSN.resolve(), ElasticSearchIdentityRecord, entity = false)
      val bestMatchResult = BestMatchEntity(ssn = Some("934645711"))
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestBadSSN.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, Elastic4sResult.empty, matched.right.get, matched.right.get.head, bestKYCMatch = Some(bestMatchResult), dto = sampleDto)

      rulecodes.get(EXX_BEST_MATCH_SSN_IS_ITIN) shouldBe Some(1.0)
    }

    "should not indicate multiple application national identifiers" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "796040440",
          "846295711"
        ),ciRowId = Array(
          "860:EXX_CHM_44872046621799776507-944"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "Multiple SSNs in identity: should not fire with extra ITIN" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true,
        betterMatchingSSNFound = true
      )


      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(ssn = Array(
          "796040440",
          "946795711"
        ),ciRowId = Array(
          "860:EXX_CHM_44872046621799776507-944",
          "960:EXX_CHM_54872046621799776507-945"
        )))
      val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should indicate national id not matched with name and address" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = true,
        notAssociatedNASCount = 1,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = true,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "wrongfirstname"
          ),
          surName = Array(
            "wronglastname"
          ),
          streetAddress = Array(
            "wrongaddress"
          )
        ))
      val searchRecordWithWrongNAS = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithWrongNAS, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithWrongNAS, searchRecordWithWrongNAS, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not trigger NAS not matched if no street address provided" in {
      val requestNoAddress = request.copy(streetAddress = None)
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = true,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "wrongfirstname"
          ),
          surName = Array(
            "wronglastname"
          ),
          streetAddress = Array(
            "wrongaddress"
          )
        ))
      val searchRecordWithWrongNAS = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = requestNoAddress.resolve(), searchRecordWithWrongNAS, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(requestNoAddress.resolve(), matched.right.get.head, searchRecordWithWrongNAS, searchRecordWithWrongNAS, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not indicate national id not matched with name and address when only firstname doesn't match" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "wrongfirstname"
          )))
      val searchRecordWithWrongNAS = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithWrongNAS, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithWrongNAS, searchRecordWithWrongNAS, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not indicate national id not matched with NAS when only lastname doesn't match, but should indicate different name" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = true,
        identityVerificationFailed = false,
        idAssocDifferentSurname = true,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          surName = Array(
            "wronglastname"
          )))
      val searchRecordWithWrongNAS = ElasticSearchIdentityRecord.copy(
        attributes = multipleSSN
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithWrongNAS, entity = true)

      val equifaxEntitiesInMergedEntity = GenericHelper.getEquifaxEntitiesForMergedEntity(
        mergedEntity = matched.right.get.head,
        scoredMatchedKyc = matched.right.get
      )

      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithWrongNAS, searchRecordWithWrongNAS, matched.right.get, matched.right.get.head,
        dto = sampleDto.copy(equifaxEntitiesInMergedEntity = equifaxEntitiesInMergedEntity)
      )

      rulecodes shouldBe expected
    }

    "should indicate ssn was issued within 3 years" in {
      val localDate = LocalDate.now
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = true,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val withYearHighLow = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          ssnYearHigh = Array(localDate.getYear.toString),
          ssnYearLow = Array(localDate.minus(1, ChronoUnit.YEARS).getYear.toString)
        ))
      val withYearHighLowIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = withYearHighLow
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), withYearHighLowIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, withYearHighLowIdentityRecord, withYearHighLowIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    "should not indicate ssn was issued within 3 years" in {
      val localDate = LocalDate.now
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val withYearHighLow = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          ssnYearHigh = Array(localDate.getYear.toString),
          ssnYearLow = Array(localDate.minus(4, ChronoUnit.YEARS).getYear.toString)
        ))
      val withYearHighLowIdentityRecord = ElasticSearchIdentityRecord.copy(
        attributes = withYearHighLow
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), withYearHighLowIdentityRecord, entity = true)
      val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, withYearHighLowIdentityRecord, withYearHighLowIdentityRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      rulecodes shouldBe expected
    }

    // R934 unit tests
    "SSN should match with the record and FirstName should pass exact match" in {
      val inputRequest = request.copy(firstName = FirstName("joseph"),nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "joseph"
          ),
          ssn = Array(
            "996040440"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN should match with record and FirstName should fail for exact match and pass for Nickname match " in {
      val inputRequest = request.copy(firstName = FirstName("josephrumph"),nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "joseph"
          ),
          ssn = Array(
            "996040440"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN should not match with record" in {
      val inputRequest = request.copy(nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN should match with record and FirstName should fail for exact match and Nickname match " in {
      val inputRequest = request.copy(firstName = FirstName("john"),nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "joseph"
          ),
          ssn = Array(
            "996040440"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN should match with record and FirstName should pass for Nickname match " in {
      val inputRequest = request.copy(firstName = FirstName("cissy"),nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "clarissa"
          ),
          ssn = Array(
            "996040440"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get,matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN should match with record and FirstName should pass for direct match " in {
      val inputRequest = request.copy(firstName = FirstName("clarissa"),nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "clarissa"
          ),
          ssn = Array(
            "996040440"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get,matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN should match with record and First Name should fail for Nickname match" in {
      val inputRequest = request.copy(firstName = FirstName("klaraa"),nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = true,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "clarissa"
          ),
          ssn = Array(
            "996040440"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get,matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "Missing NationalId evaluates to true when Input SSN is empty" in {
      val inputRequest = request.copy(nationalId = None)
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = true,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 0,
        ssnPresentOnFile = false,
        nonITINSSNPresentOnFile = false,
        ssnFoundWithNoInputSSN = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), ElasticSearchIdentityRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get,matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "Missing NationalId evaluates to false when Input SSN is not empty" in {
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = false,
        idInvalid = false,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 1,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = true,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 1,
        ssnAssociatedMultipleLastNamesNew = 1,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), ElasticSearchIdentityRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get,matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "Invalid NationalId evaluates to true when Input SSN is invalid" in {
      val inputRequest = request.copy(nationalId = Some(NationalId("111221234")))
      val expected = generateMapping(
        isIdWithDeceased = false,
        isIdentityDeceased = false,
        isAncIdWithDeceased = false,
        isAncIdentityDeceased = false,
        identityResolutionFailed = true,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = true,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = false,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true,
        ssnIssuedStateNotInAddresses = true
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), ElasticSearchIdentityRecord, entity = true)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, ElasticSearchIdentityRecord, ElasticSearchIdentityRecord, matched.right.get,matched.right.get.head, dto = sampleDto)

      ruleCodes shouldBe expected
    }

    "SSN matched in Anchor Deceased lookup" in
        {
          val inputRequest = request.copy(firstName = FirstName("clarissa"), nationalId = Some(NationalId("996040440")))
          val expected = generateMapping(
            isIdWithDeceased = true,
            isIdentityDeceased = false,
            isAncIdWithDeceased = true,
            isAncIdentityDeceased = false,
            identityResolutionFailed = false,
            idInvalid = true,
            notInPubRecords = false,
            isNationalIdMissing = false,
            idIsNotPrimary = false,
            isITIN = false,
            hasMultipleIdentities = false,
            multipleIdentitiesCount = 0,
            hasMultipleApplicationIds = false,
            notAssociatedNAS = false,
            notAssociatedNASCount = 0,
            idAssocDifferentName = false,
            identityVerificationFailed = false,
            idAssocDifferentSurname = false,
            nonUsNationalId = false,
            isRandomlyNationalId = false,
            nameAddressIdMatch = true,
            ssnAssociatedMultipleLastNames = false,
            ssnAssociatedMultipleLastNamesCount = 0,
            ssnAssociatedMultipleLastNamesNew = 1,
            firstNameNotMatchId = false,
            idIssuedWithLast3Years = false,
            isMiskeyed = false,
            ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
            )

          val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
                                             .map(_.copy(
                                               firstName = Array(
                                                 "clarissa"
                                                 ),
                                               ssn = Array(
                                                 "996040440"
                                                 )
                                               ))
          val matchedRecord = ElasticSearchIdentityRecord.copy(
            attributes = attributes
            )

          val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
          val anchorDeceasedRecord = AnchorDeceasedRecord(
            id = Some(1),
            entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
            nationalId = "996040440",
            fullName = Some("Dr. clarissa J wood"),
            streetAddress = Some("4459 despard street"),
            city = Some("atlanta"),
            zipCode = Some("30329"),
            state = Some("ga"),
            dob = Some("1963-06-29"),
            dod = Some("2020-04-18"),
            sourceVerificationCode = Some(SourceVerificationCodes.P),
            title = Some("Dr"),
            firstName = Some("clarissa"),
            middleInitial = Some("J"),
            lastName = Some("wood"),
            maturityTitle = None,
            createdAt = DateTime.parse("2022-10-10"),
            updatedAt = None,
            deletedAt = None)
          val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, Some(anchorDeceasedRecord), dto = sampleDto)

          ruleCodes shouldBe expected
        }

    "BME SSN matched in Anchor Deceased lookup" in
        {
          val inputRequest = request.copy(firstName = FirstName("clarissa"), nationalId = Some(NationalId("996040440")))
          val expected = generateMapping(
            isIdWithDeceased = true,
            isIdentityDeceased = true,
            isAncIdWithDeceased = true,
            isAncIdentityDeceased = true,
            identityResolutionFailed = false,
            idInvalid = true,
            notInPubRecords = false,
            isNationalIdMissing = false,
            idIsNotPrimary = false,
            isITIN = false,
            hasMultipleIdentities = false,
            multipleIdentitiesCount = 0,
            hasMultipleApplicationIds = false,
            notAssociatedNAS = false,
            notAssociatedNASCount = 0,
            idAssocDifferentName = false,
            identityVerificationFailed = false,
            idAssocDifferentSurname = false,
            nonUsNationalId = false,
            isRandomlyNationalId = false,
            nameAddressIdMatch = true,
            ssnAssociatedMultipleLastNames = false,
            ssnAssociatedMultipleLastNamesCount = 0,
            ssnAssociatedMultipleLastNamesNew = 1,
            firstNameNotMatchId = false,
            idIssuedWithLast3Years = false,
            isMiskeyed = false,
            ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
            )

          val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
                                             .map(_.copy(
                                               firstName = Array(
                                                 "clarissa"
                                                 ),
                                               ssn = Array(
                                                 "996040440"
                                                 )
                                               ))
          val matchedRecord = ElasticSearchIdentityRecord.copy(
            attributes = attributes
            )

          val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
          val anchorDeceasedRecord = AnchorDeceasedRecord(
            id = Some(1),
            entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
            nationalId = "996040440",
            fullName = Some("Dr. clarissa J wood"),
            streetAddress = Some("4459 despard street"),
            city = Some("atlanta"),
            zipCode = Some("30329"),
            state = Some("ga"),
            dob = Some("20030208"),
            dod = Some("20200418"),
            sourceVerificationCode = Some(SourceVerificationCodes.P),
            title = Some("Dr"),
            firstName = Some("clarissa"),
            middleInitial = Some("J"),
            lastName = Some("wood"),
            maturityTitle = None,
            createdAt = DateTime.parse("2022-10-10"),
            updatedAt = None,
            deletedAt = None)
          val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, Some(anchorDeceasedRecord), Some(anchorDeceasedRecord), dto = sampleDto)

          ruleCodes shouldBe expected
        }

    "BME SSN matched in Anchor Deceased lookup but less than 2.5 match" in
        {
          val inputRequest = request.copy(firstName = FirstName("clarissa"), nationalId = Some(NationalId("996040440")))
          val expected = generateMapping(
            isIdWithDeceased = true,
            isIdentityDeceased = false,
            isAncIdWithDeceased = true,
            isAncIdentityDeceased = false,
            identityResolutionFailed = false,
            idInvalid = true,
            notInPubRecords = false,
            isNationalIdMissing = false,
            idIsNotPrimary = false,
            isITIN = false,
            hasMultipleIdentities = false,
            multipleIdentitiesCount = 0,
            hasMultipleApplicationIds = false,
            notAssociatedNAS = false,
            notAssociatedNASCount = 0,
            idAssocDifferentName = false,
            identityVerificationFailed = false,
            idAssocDifferentSurname = false,
            nonUsNationalId = false,
            isRandomlyNationalId = false,
            nameAddressIdMatch = true,
            ssnAssociatedMultipleLastNames = false,
            ssnAssociatedMultipleLastNamesCount = 0,
            ssnAssociatedMultipleLastNamesNew = 1,
            firstNameNotMatchId = false,
            idIssuedWithLast3Years = false,
            isMiskeyed = false,
            ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
            )

          val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
                                             .map(_.copy(
                                               firstName = Array(
                                                 "clarissa"
                                                 ),
                                               ssn = Array(
                                                 "996040440"
                                                 )
                                               ))
          val matchedRecord = ElasticSearchIdentityRecord.copy(
            attributes = attributes
            )

          val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
          val anchorDeceasedRecord = AnchorDeceasedRecord(
            id = Some(1),
            entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
            nationalId = "996040440",
            fullName = Some("Dr. clarissa J wood"),
            streetAddress = Some("4459 despard street"),
            city = Some("atlanta"),
            zipCode = Some("30329"),
            state = Some("ga"),
            dob = Some("19630208"),
            dod = Some("20200418"),
            sourceVerificationCode = Some(SourceVerificationCodes.P),
            title = Some("Dr"),
            firstName = Some("clarissa"),
            middleInitial = Some("J"),
            lastName = Some("wood"),
            maturityTitle = None,
            createdAt = DateTime.parse("2022-10-10"),
            updatedAt = None,
            deletedAt = None)
          val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, Some(anchorDeceasedRecord), Some(anchorDeceasedRecord), dto = sampleDto)

          ruleCodes shouldBe expected
        }
    "should exclude prefilled ssn's & should return ssn count as 2 " in
        {
          val expected = generateMapping(
            isIdWithDeceased = false,
            isIdentityDeceased = false,
            isAncIdWithDeceased = false,
            isAncIdentityDeceased = false,
            identityResolutionFailed = false,
            idInvalid = false,
            notInPubRecords = false,
            isNationalIdMissing = false,
            idIsNotPrimary = false,
            isITIN = false,
            hasMultipleIdentities = false,
            multipleIdentitiesCount = 0,
            hasMultipleApplicationIds = false,
            notAssociatedNAS = false,
            notAssociatedNASCount = 0,
            idAssocDifferentName = false,
            identityVerificationFailed = false,
            idAssocDifferentSurname = false,
            nonUsNationalId = false,
            isRandomlyNationalId = true,
            nameAddressIdMatch = true,
            ssnAssociatedMultipleLastNames = false,
            ssnAssociatedMultipleLastNamesCount = 0,
            ssnAssociatedMultipleLastNamesNew = 1,
            firstNameNotMatchId = false,
            idIssuedWithLast3Years = false,
            isMiskeyed = false,
            ssnCount = 2,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
            )


          val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
                                              .map(_.copy(ssn = Array(
                                                "796040440",
                                                "846295711",
                                                "000005711",
                                                "000000440"
                                                )))
          val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
            attributes = multipleSSN
            )

          val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = false)
          val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

          rulecodes shouldBe expected
        }

    "should exclude prefilled ssn's & should return ssn count as 3 " in
        {
          val expected = generateMapping(
            isIdWithDeceased = false,
            isIdentityDeceased = false,
            isAncIdWithDeceased = false,
            isAncIdentityDeceased = false,
            identityResolutionFailed = false,
            idInvalid = false,
            notInPubRecords = false,
            isNationalIdMissing = false,
            idIsNotPrimary = false,
            isITIN = false,
            hasMultipleIdentities = false,
            multipleIdentitiesCount = 0,
            hasMultipleApplicationIds = false,
            notAssociatedNAS = false,
            notAssociatedNASCount = 0,
            idAssocDifferentName = false,
            identityVerificationFailed = false,
            idAssocDifferentSurname = false,
            nonUsNationalId = false,
            isRandomlyNationalId = true,
            nameAddressIdMatch = true,
            ssnAssociatedMultipleLastNames = false,
            ssnAssociatedMultipleLastNamesCount = 0,
            ssnAssociatedMultipleLastNamesNew = 1,
            firstNameNotMatchId = false,
            idIssuedWithLast3Years = false,
            isMiskeyed = false,
            ssnCount = 3,
            ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
            )


          val multipleSSN = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
                                              .map(_.copy(ssn = Array(
                                                "796040440",
                                                "846295711",
                                                "000005711",
                                                "000000440",
                                                "000001234",
                                                "1234"
                                                )))
          val searchRecordWithMultipleIds = ElasticSearchIdentityRecord.copy(
            attributes = multipleSSN
            )

          val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), searchRecordWithMultipleIds, entity = false)
          val rulecodes = EntityNationalIDRuleCodeResolver.generate(request.resolve(), matched.right.get.head, searchRecordWithMultipleIds, searchRecordWithMultipleIds, matched.right.get, matched.right.get.head, dto = sampleDto)

          rulecodes shouldBe expected
        }

    "should fire IdentityDeceased based on AnchorRecordUsingInputSSN" in {
      val inputRequest = request.copy(firstName = FirstName("clarissa"), surName = SurName("wood"), dob = Some(DOB("19630629")), nationalId = Some(NationalId("996040440")))
      val expected = generateMapping(
        isIdWithDeceased = true,
        isIdentityDeceased = true,
        isAncIdWithDeceased = true,
        isAncIdentityDeceased = true,
        identityResolutionFailed = false,
        idInvalid = true,
        notInPubRecords = false,
        isNationalIdMissing = false,
        idIsNotPrimary = false,
        isITIN = false,
        hasMultipleIdentities = false,
        multipleIdentitiesCount = 0,
        hasMultipleApplicationIds = false,
        notAssociatedNAS = false,
        notAssociatedNASCount = 0,
        idAssocDifferentName = false,
        identityVerificationFailed = false,
        idAssocDifferentSurname = false,
        nonUsNationalId = false,
        isRandomlyNationalId = false,
        nameAddressIdMatch = true,
        ssnAssociatedMultipleLastNames = false,
        ssnAssociatedMultipleLastNamesCount = 0,
        ssnAssociatedMultipleLastNamesNew = 0,
        firstNameNotMatchId = false,
        idIssuedWithLast3Years = false,
        isMiskeyed = false,
        ssnCount = 1,
        ssnPresentOnFile = true,
        nonITINSSNPresentOnFile = true
      )

      val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes).asInstanceOf[Array[Records]]
        .map(_.copy(
          firstName = Array(
            "clarissa"
          ),
          surName = Array(
            "wood"
          ),
          ssn = Array(
            "996040440"
          ),
          dob = Array(
            "19630629"
          )
        ))
      val matchedRecord = ElasticSearchIdentityRecord.copy(
        attributes = attributes
      )

      val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecord, entity = true)
      val anchorDeceasedRecord = AnchorDeceasedRecord(
        id = Some(1),
        entityId = "e91e91f3-dae9-47fb-a2ee-a2c7dc4439a1",
        nationalId = "996040440",
        fullName = Some("Dr. clarissa J wood"),
        streetAddress = Some("4459 despard street"),
        city = Some("atlanta"),
        zipCode = Some("30329"),
        state = Some("ga"),
        dob = Some("1963-06-29"),
        dod = Some("2020-04-18"),
        sourceVerificationCode = Some(SourceVerificationCodes.P),
        title = Some("Dr"),
        firstName = Some("clarissa"),
        middleInitial = Some("J"),
        lastName = Some("wood"),
        maturityTitle = None,
        createdAt = DateTime.parse("2022-10-10"),
        updatedAt = None,
        deletedAt = None)
      val ruleCodes = EntityNationalIDRuleCodeResolver.generate(inputRequest.resolve(), matched.right.get.head, matchedRecord, matchedRecord, matched.right.get, matched.right.get.head, Some(anchorDeceasedRecord), dto = sampleDto)

      ruleCodes shouldBe expected
    }
  }

  "better matching ssn found in file=true: inputSsn = random + some ssn on file nonrandom" in {
    val ssnArray = Array("065025849", "138255849")
    val inputRequest = request.copy(
      nationalId = Some(NationalId(ssnArray(1)))
    )
    val expected = generateMapping(
      isIdWithDeceased = false,
      isIdentityDeceased = false,
      isAncIdWithDeceased = false,
      isAncIdentityDeceased = false,
      identityResolutionFailed = false,
      idInvalid = false,
      notInPubRecords = false,
      isNationalIdMissing = false,
      idIsNotPrimary = true,
      isITIN = false,
      hasMultipleIdentities = false,
      multipleIdentitiesCount = 1,
      hasMultipleApplicationIds = false,
      notAssociatedNAS = false,
      notAssociatedNASCount = 0,
      idAssocDifferentName = false,
      identityVerificationFailed = false,
      idAssocDifferentSurname = false,
      nonUsNationalId = false,
      isRandomlyNationalId = true,
      nameAddressIdMatch = true,
      ssnAssociatedMultipleLastNames = false,
      ssnAssociatedMultipleLastNamesCount = 0,
      ssnAssociatedMultipleLastNamesNew = 1,
      firstNameNotMatchId = false,
      idIssuedWithLast3Years = false,
      isMiskeyed = false,
      ssnCount = ssnArray.length,
      ssnPresentOnFile = true,
      nonITINSSNPresentOnFile = true,
      betterMatchingSSNFound = true,
      ssnIssuedStateNotInAddresses = false
    )

    val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes)
      .map(_.copy(
        ssn = ssnArray
      ))
    val matchedRecords = ElasticSearchIdentityRecord.copy(
      attributes = attributes
    )

    val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecords, entity = true)

    val ruleCodes = EntityNationalIDRuleCodeResolver.generate(
      searchRequestResolved = inputRequest.resolve(),
      bestMatch = matched.right.get.head,
      foundIdentityResults = matchedRecords,
      nationalIdQueryResults = matchedRecords,
      scoredMatches = matched.right.get,
      bestOfTwoSources = matched.right.get.head,
      dto = sampleDto
    )

    ruleCodes shouldBe expected
  }

  "better matching ssn found in file=false: inputSSN = random + all ssn on file random" in {
    val ssnArray = Array("017945849", "138255849")
    val inputRequest = request.copy(
      nationalId = Some(NationalId(ssnArray(1)))
    )
    val expected = generateMapping(
      isIdWithDeceased = false,
      isIdentityDeceased = false,
      isAncIdWithDeceased = false,
      isAncIdentityDeceased = false,
      identityResolutionFailed = false,
      idInvalid = false,
      notInPubRecords = false,
      isNationalIdMissing = false,
      idIsNotPrimary = true,
      isITIN = false,
      hasMultipleIdentities = false,
      multipleIdentitiesCount = 1,
      hasMultipleApplicationIds = false,
      notAssociatedNAS = false,
      notAssociatedNASCount = 0,
      idAssocDifferentName = false,
      identityVerificationFailed = false,
      idAssocDifferentSurname = false,
      nonUsNationalId = false,
      isRandomlyNationalId = true,
      nameAddressIdMatch = true,
      ssnAssociatedMultipleLastNames = false,
      ssnAssociatedMultipleLastNamesCount = 0,
      ssnAssociatedMultipleLastNamesNew = 1,
      firstNameNotMatchId = false,
      idIssuedWithLast3Years = false,
      isMiskeyed = false,
      ssnCount = ssnArray.length,
      ssnPresentOnFile = true,
      nonITINSSNPresentOnFile = true,
      betterMatchingSSNFound = false,
      ssnIssuedStateNotInAddresses = false
    )

    val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes)
      .map(_.copy(
        ssn = ssnArray
      ))
    val matchedRecords = ElasticSearchIdentityRecord.copy(
      attributes = attributes
    )

    val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecords, entity = true)

    val ruleCodes = EntityNationalIDRuleCodeResolver.generate(
      searchRequestResolved = inputRequest.resolve(),
      bestMatch = matched.right.get.head,
      foundIdentityResults = matchedRecords,
      nationalIdQueryResults = matchedRecords,
      scoredMatches = matched.right.get,
      bestOfTwoSources = matched.right.get.head,
      dto = sampleDto
    )

    ruleCodes shouldBe expected
  }

  "better matching ssn found in file=true: inputSSN = notInPublicRecord + some ssn on file nonrandom" in {
    // bme should contain 010945849, but nationalIdQueryResults shouldnt contain that and input
    val ssnArray = Array("065025849", "010945849")
    val inputRequest = request.copy(
      nationalId = Some(NationalId(ssnArray(1)))
    )
    val expected = generateMapping(
      isIdWithDeceased = false,
      isIdentityDeceased = false,
      isAncIdWithDeceased = false,
      isAncIdentityDeceased = false,
      identityResolutionFailed = false,
      idInvalid = false,
      notInPubRecords = true,
      isNationalIdMissing = false,
      idIsNotPrimary = true,
      isITIN = false,
      hasMultipleIdentities = false,
      multipleIdentitiesCount = 1,
      hasMultipleApplicationIds = false,
      notAssociatedNAS = false,
      notAssociatedNASCount = 0,
      idAssocDifferentName = false,
      identityVerificationFailed = false,
      idAssocDifferentSurname = false,
      nonUsNationalId = false,
      isRandomlyNationalId = false,
      nameAddressIdMatch = true,
      ssnAssociatedMultipleLastNames = false,
      ssnAssociatedMultipleLastNamesCount = 1,
      ssnAssociatedMultipleLastNamesNew = 0,
      firstNameNotMatchId = false,
      idIssuedWithLast3Years = false,
      isMiskeyed = false,
      ssnCount = ssnArray.length,
      ssnPresentOnFile = true,
      nonITINSSNPresentOnFile = true,
      betterMatchingSSNFound = true,
      ssnIssuedStateNotInAddresses = true
    )

    val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes)
      .map(_.copy(
        ssn = ssnArray
      ))
    val matchedRecords = ElasticSearchIdentityRecord.copy(
      attributes = attributes
    )

    val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecords, entity = true)

    val ruleCodes = EntityNationalIDRuleCodeResolver.generate(
      searchRequestResolved = inputRequest.resolve(),
      bestMatch = matched.right.get.head,
      foundIdentityResults = matchedRecords,
      nationalIdQueryResults = ElasticSearchIdentityRecord,
      scoredMatches = matched.right.get,
      bestOfTwoSources = matched.right.get.head,
      dto = sampleDto
    )

    ruleCodes shouldBe expected
  }

  "better matching ssn found in file=false: inputSSN = notInPublicRecord + all ssn on file random" in {
    // bme should contain 010945849, but nationalIdQueryResults shouldnt contain that, other snn candidates in bme should be random,
    val ssnArray = Array("086025749", "010945849", "020945421")
    val inputRequest = request.copy(
      nationalId = Some(NationalId(ssnArray(1)))
    )
    val expected = generateMapping(
      isIdWithDeceased = false,
      isIdentityDeceased = false,
      isAncIdWithDeceased = false,
      isAncIdentityDeceased = false,
      identityResolutionFailed = false,
      idInvalid = false,
      notInPubRecords = true,
      isNationalIdMissing = false,
      idIsNotPrimary = true,
      isITIN = false,
      hasMultipleIdentities = false,
      multipleIdentitiesCount = 1,
      hasMultipleApplicationIds = false,
      notAssociatedNAS = false,
      notAssociatedNASCount = 0,
      idAssocDifferentName = false,
      identityVerificationFailed = false,
      idAssocDifferentSurname = false,
      nonUsNationalId = false,
      isRandomlyNationalId = false,
      nameAddressIdMatch = true,
      ssnAssociatedMultipleLastNames = false,
      ssnAssociatedMultipleLastNamesCount = 1,
      ssnAssociatedMultipleLastNamesNew = 0,
      firstNameNotMatchId = false,
      idIssuedWithLast3Years = false,
      isMiskeyed = false,
      ssnCount = ssnArray.length,
      ssnPresentOnFile = true,
      nonITINSSNPresentOnFile = true,
      betterMatchingSSNFound = false,
      ssnIssuedStateNotInAddresses = true
    )

    val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes)
      .map(_.copy(
        ssn = ssnArray
      ))
    val matchedRecords = ElasticSearchIdentityRecord.copy(
      attributes = attributes
    )

    val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecords, entity = true)

    val ruleCodes = EntityNationalIDRuleCodeResolver.generate(
      searchRequestResolved = inputRequest.resolve(),
      bestMatch = matched.right.get.head,
      foundIdentityResults = matchedRecords,
      nationalIdQueryResults = ElasticSearchIdentityRecord,
      scoredMatches = matched.right.get,
      bestOfTwoSources = matched.right.get.head,
      dto = sampleDto
    )

    ruleCodes shouldBe expected
  }

  "better matching ssn found in file=false: no bme found" in {
    // bme should contain 010945849, but nationalIdQueryResults shouldn't contain that, other snn candidates in bme should be random,
    val bestMatch = BestKYCMatch(
      cluster = null,
      piiMatchResults = null,
      bestMatchedElements = scala.collection.concurrent.TrieMap[String, String](),
      mergeStatus = null,
      identityRecord = null
    )
    val matched = resultMatcher.calculateMatches(searchRequestResolved = request.resolve(), EmptyElasticSearchIdentityRecord, entity = true)

    val ruleCodes = EntityNationalIDRuleCodeResolver.generate(
      searchRequestResolved = request.resolve(),
      bestMatch = matched.right.get.head,
      foundIdentityResults = EmptyElasticSearchIdentityRecord,
      nationalIdQueryResults = EmptyElasticSearchIdentityRecord,
      scoredMatches = matched.right.get,
      bestOfTwoSources = matched.right.get.head,
      dto = sampleDto
    )

    ruleCodes.get(EXX_BETTER_MATCHING_SSN_FOUND) shouldBe Some(0.0)
  }

  "no input ssn found match when ssn found in file=true and inputSSN not passed " in {
    val ssnArray = Array("086025749")
    val inputRequest = request.copy(nationalId = None)

    val attributes = SerializationUtils.clone(ElasticSearchIdentityRecord.attributes)
      .map(_.copy(
        ssn = ssnArray
      ))
    val matchedRecords = ElasticSearchIdentityRecord.copy(
      attributes = attributes
    )

    val matched = resultMatcher.calculateMatches(searchRequestResolved = inputRequest.resolve(), matchedRecords, entity = true)

    val ruleCodes = EntityNationalIDRuleCodeResolver.generate(
      searchRequestResolved = inputRequest.resolve(),
      bestMatch = matched.right.get.head,
      foundIdentityResults = matchedRecords,
      nationalIdQueryResults = ElasticSearchIdentityRecord,
      scoredMatches = matched.right.get,
      bestOfTwoSources = matched.right.get.head,
      dto = sampleDto
    )

    ruleCodes.get(EXX_NO_INPUT_BUT_SSN_FOUND) shouldBe Some(1.0)

  }
}
