package me.socure.common.kyc.service

import me.socure.common.kyc.model.KycEntitySearchRequest
import me.socure.common.kyc.model.es.result._
import me.socure.common.kyc.model.RecordCleanupOperationTypes._
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId

object RecordCleanupService {

  private val logger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)

  private val cleanupOperations: Map[Value, List[RecordsCleanupOperation]] = Map(
    IndividualRecord -> List(
      SerializedSSNCleanup,
      RandomizedSSNCleanup,
      InvalidSSNCleanup,
      MultipleSSNSingleDOB,
      MultipleDOBMultipleSSN,
      new MultipleDOBSingleSSN(false),
      RemoveNonCHMSSN,
      InvalidStreetAddressCleanup,
      RequestBasedNameCleanup,
      PlaceHolderNamesCleanUp,
      InitialFirstNameCleanUp,
      DOBSSNIssuanceCleanup
    ),
    PostMerge -> List(
      PrefillSSNBasedOnWeight,
      MultipleDOBMultipleSSN,
      new MultipleDOBSingleSSN(true),
      SourcePrioritizationDobCleanUp,
      FrequencyBasedDobCleanUp,
      DOBSSNIssuanceCleanup
    )
  )

  def performCleanup(request: KycEntitySearchRequest, records: Records, identityRecord: Option[IdentityRecord], operationType: RecordCleanupOperationType, workflow: Option[String] = None)(implicit trxId: TrxId): RecordCleanupOperationResult = {
    metrics.time("cleanup.duration", s"type:${operationType.toString}") {
      val operations = cleanupOperations(operationType)
      var isUpdated = false
      val result = operations.foldLeft(RecordCleanupOperationResult(updated = false, records, identityRecord)) { (result, op) =>
        val newResult = op(RecordCleanupOperationInput(request, result.records, result.identityRecord))
        if (newResult.updated) {
          isUpdated = true
          if (operationType.equals(PostMerge)) {
            logger.info(s"${op.name}: ${workflow.getOrElse("NA")}: Updated ${result.records.clusterId} in PostMerge")
          }
          metrics.increment("affected.count", s"operation:${op.name}", s"type:${operationType.toString}", s"workflow:${workflow.getOrElse("NA")}")
        }
        newResult
      }
      result.copy(updated = isUpdated)
    }
  }
}
