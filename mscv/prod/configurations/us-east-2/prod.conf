threadpool {
  poolSize = 800
}

server {
  port = 5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

security.hmac {
  secret.key="""ENC(q/wezuG6c+APRUpFvJG3w5W9yayMketH5yHbuelE4skL06YCOJSkrxqWOkTKHVWoNn+eFW4flPHu)"""
  ttl=5
  time.interval=5
  strength=512
}

cors {
  allowedDomains = [
    "http://swagger.us-east-vpc.socure.be"
  ]
}

jmx {
  port = 1098
}

match.weighting {
  useControlCenter = false
  controlCentreTimeout = 5
  weights {
    default-match-weights {
      firstNameWeight = 4.0,
      lastNameWeight = 9.0,
      ssnWeight = 10.0,
      dobWeight = 6.0,
      phoneWeight = 1.0,
      emailWeight = 1.0,
      streetAddressWeight = 4.0,
      cityWeight = 3.0,
      zipWeight = 2.0,
      stateWeight = 1.0
    }
    challenger-match-weights {
      firstNameWeight = 5.0,
      lastNameWeight = 4.0,
      ssnWeight = 9.0,
      dobWeight = 9.0,
      phoneWeight = 2.0,
      emailWeight = 1.0,
      streetAddressWeight = 3.0,
      cityWeight = 2.0,
      zipWeight = 2.0,
      stateWeight = 7.0
    }
  }
}

### Remove below 2 once fix for above is deployed

default-match-weights {
  firstNameWeight=4.0,
  lastNameWeight=9.0,
  ssnWeight=10.0,
  dobWeight=6.0,
  phoneWeight=4.0,
  streetAddressWeight=4.0,
  cityWeight=3.0,
  zipWeight=2.0,
  stateWeight=1.0
}

challenger-match-weights {
  firstNameWeight=5.0,
  lastNameWeight=4.0,
  ssnWeight=9.0,
  dobWeight=9.0,
  phoneWeight=1.0,
  streetAddressWeight=3.0,
  cityWeight=2.0,
  zipWeight=2.0,
  stateWeight=7.0
}

######################

elastic {
  host = "vpc-kyc-search-prod-5qotzhfwwa3q2j6vdwjjmcdwoe.us-east-2.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-2"
  indexName = "identities-cid-efx-202504"
  querySize = 100
  dynamo {
    enabled = true
    table = "kyc_identity_data_efx_202504_unified"
  }
}

enf.elastic {
  host = "vpc-kyc-search-prod-5qotzhfwwa3q2j6vdwjjmcdwoe.us-east-2.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-2"
  indexName = "identities-cid-enf-202504"
  querySize = 100
  dynamo {
    enabled = true
    table = "kyc_identity_data_enf_202504_unified"
  }
}

bld.elastic {
  enabled = true
  host = "vpc-kyc-search-prod-5qotzhfwwa3q2j6vdwjjmcdwoe.us-east-2.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-2"
  indexName = "identities-cid-bld-asl-edv-202504"
  querySize = 100
  dynamo {
    enabled = true
    table = "kyc_identity_data_bld_asl_edv_202504_v1_unified"
    retry.count = 2
    batch.fetch {
      size = 25
      timeout = "200 milliseconds"
    }
  }
}

obitscrapper.elastic {
  enabled = true
  host =  "vpc-kyc-search-prod-scraped-pii-kubbyckopbyavzn6drdrnfyhlq.us-east-2.es.amazonaws.com"
  port = 443
  protocol = "https"
  region = "us-east-2"
  indexName = "identities-cid-deceased-data"
  querySize = 100
  timeoutInMillis = 100
  dynamo {
    enabled = true
    table = "kyc_identity_data_deceased_unified"
  }
}

alternate {
    elastic {
      host = "vpc-kyc-search-prod-5qotzhfwwa3q2j6vdwjjmcdwoe.us-east-2.es.amazonaws.com"
      port = 443
      protocol = "https"
      region = "us-east-2"
      indexName = "identities-cid-efx-202504"
      querySize = 100
      dynamo {
        enabled = true
        table = "kyc_identity_data_efx_202504_unified"
      }
    }

    enf.elastic {
      host = "vpc-kyc-search-prod-5qotzhfwwa3q2j6vdwjjmcdwoe.us-east-2.es.amazonaws.com"
      port = 443
      protocol = "https"
      region = "us-east-2"
      indexName = "identities-cid-enf-202504"
      querySize = 100
      dynamo {
        enabled = true
        table = "kyc_identity_data_enf_202504_unified"
      }
    }
    bld.elastic {
      enabled = true
      host = "vpc-kyc-search-prod-5qotzhfwwa3q2j6vdwjjmcdwoe.us-east-2.es.amazonaws.com"
      port = 443
      protocol = "https"
      region = "us-east-2"
      indexName = "identities-cid-bld-asl-edv-202504"
      querySize = 100
      dynamo {
        enabled = true
        table = "kyc_identity_data_bld_asl_edv_202504_v1_unified"
      }
    }
}

ssn.dynamodb {
  table = "kyc_identity_data_202504_ssn_tbl_unified"
  correctionlog = {
    enabled = false
    table = "kyc_ssn_corrections_tbl_prod"
  }
}

docv.datasource {
  elastic {
    enabled = true
    host = "vpc-kyc-search-prod-docv-txn-2eikfxv2o4ikxok34j4av23izi.us-east-2.es.amazonaws.com"
    port = 443
    protocol = "https"
    region = "us-east-2"
    indexName = "identities-cid-docv-new"
    querySize = 100
    timeoutInMillis = 150
  }
}

#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "pltv2-ue2-idplus-global-settings-************-us-east-2"
  }
  memcached {
    host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
    port=11211
    ttl="24 hours"
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Control Center==========================#

enformiom.merge.suppressed {
  accountIds = []
}

#==================== SQS for Display BME in Admin Dashboard ==========================#

sqs {
  primary {
    region = "us-east-2",
    queue = "transaction-resolved-entity-worker-prod-2984d97c"
  }
  secondary {
    region = "us-west-2",
    queue = "transaction-resolved-entity-worker-prod-2984d97c"
  }
  retry {
    initial.backoff = "1 seconds",
    max.backoff = "32 seconds",
    multiplier = 2,
    max.attempts = 5
  }
}

memcached {
  host = "pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com"
  port = 11211
}

#===================Account Service===================#
account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"
  region = "us-east-2"
  hmac {
    secret.key="""ENC(BrsK1r5yNmghO4setxAicVEikCUXPg9jQgAb1mlGCH7kSybLP3ZDwuPqGeca2PImr/utVOfurHznvHXRv0NDLg==)"""
    strength=512
    realm="Socure"
    version = "1.0"
  }
}
#===================Account Service===================#

ecbsv.inhouse {
  enabled = true
  timeout = 200
  db = {
    fetch.limit = 10
    useLocalDB = false
    retry {
      interval = "10 milliseconds"
      attempts = 2
    }
  }
}

dynamic.control.center {
  s3 {
    bucketName = "pltv2-ue2-globalconfig-************-us-east-2"
  }
  memcached {
    host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
    cache.read.timeout.milliseconds = 600
  }
}

transaction-auditing {
  threadpool {
    poolSize=30
  }

  aws {

    maxRetries = 10

    primary {
      sqs {
        region=us-east-2
        transaction {
          queueName=pltv2-ue2-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=pltv2-ue2-third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-2
        transaction {
          queueName=pltv2-ue2-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=pltv2-ue2-third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-1
        transaction {
          queueName=pltv2-ue2-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=pltv2-ue2-third-party-transaction-auditing-prod
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-prod-************-us-east-2"
      }
      third-party {
        region=us-east-2
        bucket="thirdparty-stats-prod-************-us-east-2"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}

unique.id {
  read.only = true
  sqs.push.enabled = true
  retry {
    interval = "10 milliseconds"
    attempts = 2
  }
  dynamodb {
    tableName = "kyc_entity_unification_prod"
  }
}

anchor.enabled=true
dynamo.enabled=true
dynamo.kyc.config.table_name=kyc_table_config_prod
dynamo {
  scheduler {
    poolsize = 2
    interval.seconds = 120
  }
  region = "us-east-2"
  maxConcurrency = 100
}


unified.entity {
  elastic {
    enabled = false
    host="vpc-kyc-dummy-5vurv7zew47dcuo2bnegddzx34.us-east-2.es.amazonaws.com"
    port=443
    protocol="https"
    region="us-east-2"
    indexName="unified-entity-data-01"
    querySize=10
  }
  sqs {
    messagePush = false
    primary {
      region = "us-east-2",
      queue = "transaction-resolved-entity-worker-prod-2984d97c"
    }
    secondary {
      region = "us-west-2",
      queue = "transaction-resolved-entity-worker-prod-2984d97c"
    }
    s3.fallback {
      region = "us-east-2",
      bucket = "transaction-resolved-entity-prod-************-us-east-2",
      basePath = "UnifiedEntity",
      kmsId = "arn:aws:kms:us-east-2:************:key/fc013ee0-7f8f-427a-a023-011cda93c3b4"
    }
    retry {
      initial.backoff = "1 seconds",
      max.backoff = "32 seconds",
      multiplier = 2,
      max.attempts = 5
    }
  }
}

server.metrics.enabled = false

internal_entity {
  elastic {
    host = "vpc-kyc-search-prod-docv-txn-2eikfxv2o4ikxok34j4av23izi.us-east-2.es.amazonaws.com"
    port = 443
    protocol = "https"
    region = "us-east-2"
    indexName = "identities-cid-internal-entity"
    querySize = 15
  }
  timeout = 1000
  enabled = true
  dynamo.table = "kyc_internal_entity_prod"
}

show {
  top20obitRecords = false
  nationalIdQueryResults = false
  additonalMatches = false
  top20EquifaxEntities = false
  top20EnformionEntities = false
}

deceasedConfigName = "elasticSearchConnectionsPRODConf"

#================ Smarty Streets ================#
smartystreets.service {
  endpoint = "http://smartystreets"
}

useNewDateRangeQuery = true

inhouse {
  dynamo {
    region = "us-east-2"
    concurrency = 100
    table = "ecbsv_inhouse_data_prod"
    index = "ecbsv_inhouse_data_gsi_ssn_hmac_created_at"
    hmacSecret = "ENC(xAUAltacXDp8n4XmZtwc5e6mM6/MBqWS9pLmnp+LDBfgdXL278+km37gAmUF8HxpnjYmpzpy92N5VCzlYvMB8g==)"
  }
}

rulecode {
  configTable = "rc_config_unified"
  currentConfigKey = "rc_table_config_prod"
  table.refresh.enabled = true
  scheduler {
    poolsize = 2
    interval.seconds = 120
  }
  tower_data_table_name = {
    "table_name" : "towerdata_email_lookup",
    "email_first_seen_path" : "eam.date_first_seen",
    "is_data_compressed" : true
  }
  full_contact_table_name = {
    "table_name" : "fullcontact_email_lookup",
    "email_first_seen_path" : "persons.emailFirstSeen",
    "email_last_seen_path" : "persons.emailLastSeen",
    "is_data_compressed" : true
  }
  production_data_table_name = {
    "table_name" : "sv4_correlation_features",
    "email_first_seen_path" : "fsd",
    "email_last_seen_path" : "lsd",
    "is_data_compressed" : false
  }
}

email.processing.enabled = false

attom {
    enabled = true
    timeout = "300 milliseconds"
}
